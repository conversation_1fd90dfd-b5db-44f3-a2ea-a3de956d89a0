{"name": "2careconnectorlite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 4002 --strictPort", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate-types": "supabase gen types typescript --project-id yekarqanirdkdckimpna --schema care_connector > src/lib/database.types.ts"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@supabase/supabase-js": "^2.50.3", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.81.5", "clsx": "^2.1.1", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-router-dom": "^6.30.1", "recharts": "^3.1.0", "tailwind-merge": "^2.6.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "typescript": "^5.5.3", "vite": "^5.4.1"}}