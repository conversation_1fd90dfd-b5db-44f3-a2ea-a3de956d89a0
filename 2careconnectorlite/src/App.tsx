import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Header from './components/Header'
import Home from './pages/Home'
import Caregivers from './pages/Caregivers'
import Companions from './pages/Companions'
import Professionals from './pages/Professionals'
import CareCheckers from './pages/CareCheckers'
import CareGroups from './pages/CareGroups'
import CareGroupDetailPage from './pages/CareGroupDetailPage'
import CareGroupSettingsPage from './pages/CareGroupSettingsPage'
import CareGroupMembersPage from './pages/CareGroupMembersPage'
import CareGroupEventsPage from './pages/CareGroupEventsPage'
import JoinGroup from './pages/JoinGroup'
import ProviderProfile from './pages/ProviderProfile'
import SignIn from './pages/SignIn'
import Auth from './pages/Auth'
import Dashboard from './pages/Dashboard'
import GetStarted from './pages/GetStarted'
import HowItWorks from './pages/HowItWorks'
import Features from './pages/Features'
import SharedCalendars from './pages/SharedCalendars'
import SecureMessaging from './pages/SecureMessaging'
import TaskManagement from './pages/TaskManagement'
import AIAssistant from './pages/AIAssistant'
import BookingDetailPage from './pages/BookingDetailPage'
import MyBookingsPage from './pages/MyBookingsPage'
import CreateBookingPage from './pages/CreateBookingPage'
import RescheduleBookingPage from './pages/RescheduleBookingPage'
import SubmitBookingReviewPage from './pages/SubmitBookingReviewPage'
import UserCaregiverBookings from './pages/UserCaregiverBookings'
import UserCompanionBookings from './pages/UserCompanionBookings'
import UserCareCheckerBookings from './pages/UserCareCheckerBookings'
import BookingConfirmationPage from './pages/BookingConfirmationPage'
import BookingCancellationPage from './pages/BookingCancellationPage'
import BookingModificationPage from './pages/BookingModificationPage'
import BookingHistoryPage from './pages/BookingHistoryPage'
import ProviderBookingCalendarPage from './pages/ProviderBookingCalendarPage'
import BookingAnalyticsPage from './pages/BookingAnalyticsPage'
import BookingNotificationsPage from './pages/BookingNotificationsPage'
import BookingSearchPage from './pages/BookingSearchPage'
import BookingPreferencesPage from './pages/BookingPreferencesPage'
import BookingReportsPage from './pages/BookingReportsPage'
import BookingRemindersPage from './pages/BookingRemindersPage'
import BookingPaymentPage from './pages/BookingPaymentPage'
import BookingInvoicePage from './pages/BookingInvoicePage'
import BookingTransactionPage from './pages/BookingTransactionPage'
import BookingStatusPage from './pages/BookingStatusPage'
import BookingAvailabilityPage from './pages/BookingAvailabilityPage'
import BookingRecurringPage from './pages/BookingRecurringPage'
import BookingOverviewPage from './pages/BookingOverviewPage'
import AdminAnalytics from './pages/AdminAnalytics'
import AdminContentModeration from './pages/AdminContentModeration'
import AdminSettings from './pages/AdminSettings'
import AdminDashboardPage from './pages/AdminDashboardPage'
import ProviderManagement from './pages/ProviderManagement'
import MessagingSystem from './pages/MessagingSystem'
import './index.css'

const queryClient = new QueryClient()

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div style={{ minHeight: '100vh', backgroundColor: 'var(--bg-primary)' }}>
          <Header />
          <main>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/caregivers" element={<Caregivers />} />
              <Route path="/companions" element={<Companions />} />
              <Route path="/professionals" element={<Professionals />} />
              <Route path="/care-checkers" element={<CareCheckers />} />
              <Route path="/care-groups" element={<CareGroups />} />
              <Route path="/care-groups/:id" element={<CareGroupDetailPage />} />
              <Route path="/care-groups/:id/settings" element={<CareGroupSettingsPage />} />
              <Route path="/care-groups/:id/members" element={<CareGroupMembersPage />} />
              <Route path="/care-groups/:id/events" element={<CareGroupEventsPage />} />
              <Route path="/browse-groups" element={<CareGroups />} />
              <Route path="/join-group" element={<JoinGroup />} />
              <Route path="/provider/:providerType/:providerId" element={<ProviderProfile />} />
              <Route path="/sign-in" element={<SignIn />} />
              <Route path="/auth" element={<Auth />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/get-started" element={<GetStarted />} />
              <Route path="/how-it-works" element={<HowItWorks />} />
              <Route path="/features" element={<Features />} />
              <Route path="/shared-calendars" element={<SharedCalendars />} />
              <Route path="/secure-messaging" element={<SecureMessaging />} />
              <Route path="/task-management" element={<TaskManagement />} />
              <Route path="/ai-assistant" element={<AIAssistant />} />
              <Route path="/booking/:bookingId" element={<BookingDetailPage />} />
              <Route path="/my-bookings" element={<MyBookingsPage />} />
              <Route path="/create-booking/:providerType/:providerId" element={<CreateBookingPage />} />
              <Route path="/reschedule-booking/:bookingId" element={<RescheduleBookingPage />} />
              <Route path="/submit-review/:bookingId" element={<SubmitBookingReviewPage />} />
              <Route path="/caregiver-bookings" element={<UserCaregiverBookings />} />
              <Route path="/companion-bookings" element={<UserCompanionBookings />} />
              <Route path="/care-checker-bookings" element={<UserCareCheckerBookings />} />
              <Route path="/booking-confirmation/:bookingId" element={<BookingConfirmationPage />} />
              <Route path="/cancel-booking/:bookingId" element={<BookingCancellationPage />} />
              <Route path="/modify-booking/:bookingId" element={<BookingModificationPage />} />
              <Route path="/booking-history" element={<BookingHistoryPage />} />
              <Route path="/provider-calendar/:providerId" element={<ProviderBookingCalendarPage />} />
              <Route path="/booking-analytics" element={<BookingAnalyticsPage />} />
              <Route path="/booking-notifications" element={<BookingNotificationsPage />} />
              <Route path="/booking-search" element={<BookingSearchPage />} />
              <Route path="/booking-preferences" element={<BookingPreferencesPage />} />
              <Route path="/booking-reports" element={<BookingReportsPage />} />
              <Route path="/booking-reminders" element={<BookingRemindersPage />} />
              <Route path="/booking-payment" element={<BookingPaymentPage />} />
              <Route path="/booking-invoice" element={<BookingInvoicePage />} />
              <Route path="/booking-transaction" element={<BookingTransactionPage />} />
              <Route path="/booking-status" element={<BookingStatusPage />} />
              <Route path="/booking-availability" element={<BookingAvailabilityPage />} />
              <Route path="/booking-recurring" element={<BookingRecurringPage />} />
              <Route path="/booking-overview" element={<BookingOverviewPage />} />
              <Route path="/admin-dashboard" element={<AdminDashboardPage />} />
              <Route path="/admin-analytics" element={<AdminAnalytics />} />
              <Route path="/admin-content-moderation" element={<AdminContentModeration />} />
              <Route path="/admin-settings" element={<AdminSettings />} />
              <Route path="/provider-management" element={<ProviderManagement />} />
              <Route path="/messaging-system" element={<MessagingSystem />} />
            </Routes>
          </main>
        </div>
      </Router>
    </QueryClientProvider>
  )
}

export default App
