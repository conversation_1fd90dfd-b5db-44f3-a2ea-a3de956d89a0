import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { ChevronDown } from 'lucide-react'

export default function Header() {
  const [findCareOpen, setFindCareOpen] = useState(false)
  const [careGroupsOpen, setCareGroupsOpen] = useState(false)
  const location = useLocation()

  // Close all dropdowns when route changes for proper Apple Mac desktop UX
  useEffect(() => {
    setFindCareOpen(false)
    setCareGroupsOpen(false)
  }, [location.pathname])

  return (
    <header
      className="w-full px-8 py-4 flex justify-between items-center relative"
      style={{
        backgroundColor: 'var(--bg-primary)',
        borderBottom: '1px solid var(--border-light)'
      }}
    >
      {/* Logo */}
      <Link to="/" className="flex items-center no-underline gap-2">
        <div
          className="w-8 h-8 rounded flex items-center justify-center text-white font-bold text-sm"
          style={{ backgroundColor: 'var(--primary)' }}
        >
          CC
        </div>
        <span
          className="text-xl font-semibold"
          style={{ color: 'var(--text-primary)' }}
        >
          Care Connector
        </span>
      </Link>

      {/* Navigation */}
      <nav className="flex items-center gap-8">
        {/* Find Care Dropdown */}
        <div className="relative">
          <button
            onClick={() => setFindCareOpen(!findCareOpen)}
            className="bg-transparent border-none flex items-center gap-1 no-underline font-medium text-sm cursor-pointer"
            style={{ color: 'var(--text-secondary)' }}
          >
            Find Care
            <ChevronDown className="w-4 h-4" />
          </button>
          
          {findCareOpen && (
            <div
              className="absolute top-full left-0 rounded py-2 min-w-[150px] z-50 shadow-lg"
              style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)'
              }}
            >
              <Link
                to="/caregivers"
                onClick={() => setFindCareOpen(false)}
                className="block py-2 px-4 no-underline text-sm hover:bg-gray-50"
                style={{ color: 'var(--text-secondary)' }}
              >
                Caregivers
              </Link>
              <Link
                to="/companions"
                onClick={() => setFindCareOpen(false)}
                className="block py-2 px-4 no-underline text-sm hover:bg-gray-50"
                style={{ color: 'var(--text-secondary)' }}
              >
                Companions
              </Link>
              <Link
                to="/professionals"
                onClick={() => setFindCareOpen(false)}
                className="block py-2 px-4 no-underline text-sm hover:bg-gray-50"
                style={{ color: 'var(--text-secondary)' }}
              >
                Professionals
              </Link>
              <Link
                to="/care-checkers"
                onClick={() => setFindCareOpen(false)}
                className="block py-2 px-4 no-underline text-sm hover:bg-gray-50"
                style={{ color: 'var(--text-secondary)' }}
              >
                Care Checkers
              </Link>
            </div>
          )}
        </div>

        {/* Care Groups Dropdown */}
        <div className="relative">
          <button
            onClick={() => setCareGroupsOpen(!careGroupsOpen)}
            className="bg-transparent border-none flex items-center gap-1 no-underline font-medium text-sm cursor-pointer"
            style={{ color: 'var(--text-secondary)' }}
          >
            Care Groups
            <ChevronDown className="w-4 h-4" />
          </button>

          {careGroupsOpen && (
            <div
              className="absolute top-full left-0 rounded py-2 min-w-[150px] z-50 shadow-lg"
              style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)'
              }}
            >
              <Link
                to="/browse-groups"
                onClick={() => setCareGroupsOpen(false)}
                className="block py-2 px-4 no-underline text-sm hover:bg-gray-50"
                style={{ color: 'var(--text-secondary)' }}
              >
                Browse Groups
              </Link>
              <Link
                to="/join-group"
                onClick={() => setCareGroupsOpen(false)}
                className="block py-2 px-4 no-underline text-sm hover:bg-gray-50"
                style={{ color: 'var(--text-secondary)' }}
              >
                Join a Group
              </Link>
            </div>
          )}
        </div>

        <Link
          to="/how-it-works"
          className="no-underline font-medium text-sm"
          style={{ color: 'var(--text-secondary)' }}
        >
          How it Works
        </Link>
        <Link
          to="/features"
          className="no-underline font-medium text-sm"
          style={{ color: 'var(--text-secondary)' }}
        >
          Features
        </Link>
      </nav>

      {/* Auth Buttons */}
      <div className="flex items-center gap-4">
        <Link
          to="/sign-in"
          className="no-underline font-medium text-sm"
          style={{ color: 'var(--text-secondary)' }}
        >
          Sign In
        </Link>
        <Link
          to="/get-started"
          className="no-underline text-white py-2 px-4 rounded font-medium text-sm"
          style={{ backgroundColor: 'var(--primary)' }}
        >
          Get Started
        </Link>
      </div>
    </header>
  )
}
