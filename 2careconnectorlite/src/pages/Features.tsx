import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Shield, 
  Clock, 
  Users, 
  Calendar, 
  MessageSquare, 
  CheckSquare,
  Smartphone,
  Lock,
  Star,
  Bell,
  FileText,
  Activity
} from 'lucide-react'

export default function Features() {
  const coreFeatures = [
    {
      icon: Users,
      title: "Verified Care Professionals",
      description: "Access a network of thoroughly vetted caregivers, companions, and healthcare professionals.",
      features: ["Background checks", "Skill verification", "Reviews & ratings", "Insurance verification"]
    },
    {
      icon: Calendar,
      title: "Smart Scheduling",
      description: "Coordinate care schedules with intelligent calendar management and automated reminders.",
      features: ["Shared calendars", "Automated reminders", "Conflict detection", "Recurring appointments"]
    },
    {
      icon: MessageSquare,
      title: "Secure Communication",
      description: "HIPAA-compliant messaging platform for seamless care team communication.",
      features: ["Encrypted messaging", "File sharing", "Group conversations", "Care updates"]
    },
    {
      icon: CheckSquare,
      title: "Task Management",
      description: "Track daily care tasks, medications, and care goals with real-time updates.",
      features: ["Care checklists", "Medication tracking", "Progress monitoring", "Goal setting"]
    }
  ]

  const additionalFeatures = [
    {
      icon: Shield,
      title: "Privacy & Security",
      description: "Bank-level encryption and HIPAA compliance ensure your data stays protected."
    },
    {
      icon: Smartphone,
      title: "Mobile-First Design",
      description: "Access your care network anywhere with our responsive mobile platform."
    },
    {
      icon: Bell,
      title: "Smart Notifications",
      description: "Get timely alerts for appointments, medication reminders, and care updates."
    },
    {
      icon: Activity,
      title: "Care Analytics",
      description: "Track care progress with detailed insights and reporting."
    },
    {
      icon: FileText,
      title: "Document Management",
      description: "Securely store and share medical records, care plans, and important documents."
    },
    {
      icon: Star,
      title: "Quality Assurance",
      description: "Continuous monitoring and feedback ensure the highest quality of care."
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Comprehensive Care Features
          </h1>
          <p className="mt-4 max-w-3xl mx-auto text-xl text-gray-600">
            Everything you need to coordinate, manage, and deliver exceptional care for your loved ones.
          </p>
        </div>

        {/* Core Features */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Core Platform Features
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {coreFeatures.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="bg-white rounded-xl shadow-lg p-8">
                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-teal-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {feature.description}
                      </p>
                      <ul className="space-y-2">
                        {feature.features.map((item, idx) => (
                          <li key={idx} className="flex items-center text-sm text-gray-600">
                            <CheckSquare className="w-4 h-4 text-teal-500 mr-2 flex-shrink-0" />
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Additional Features Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Additional Platform Benefits
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {additionalFeatures.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center">
                  <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-6 h-6 text-teal-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {feature.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Security Section */}
        <div className="bg-teal-50 rounded-2xl p-8 mb-16">
          <div className="text-center">
            <Lock className="w-16 h-16 text-teal-600 mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Enterprise-Grade Security
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-3xl mx-auto">
              Your care data is protected with the same security standards used by banks and healthcare institutions.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">HIPAA Compliant</h3>
                <p className="text-sm text-gray-600">Full compliance with healthcare privacy regulations</p>
              </div>
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">End-to-End Encryption</h3>
                <p className="text-sm text-gray-600">All data encrypted in transit and at rest</p>
              </div>
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">SOC 2 Certified</h3>
                <p className="text-sm text-gray-600">Audited security controls and processes</p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Experience the Difference
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of families who have transformed their care coordination with Care Connector.
          </p>
          <div className="space-x-4">
            <Link
              to="/get-started"
              className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700"
            >
              Start Free Trial
            </Link>
            <Link
              to="/how-it-works"
              className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
