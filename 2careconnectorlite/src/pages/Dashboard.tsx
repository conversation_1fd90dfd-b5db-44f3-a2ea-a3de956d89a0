import React, { useState, useEffect } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { 
  User, 
  Calendar, 
  MessageSquare, 
  Settings, 
  Heart, 
  Clock, 
  MapPin,
  Star,
  Bell,
  LogOut,
  Menu,
  X,
  Users,
  CheckCircle,
  MessageCircle,
  UserCheck,
  Activity,
  Plus,
  Search
} from 'lucide-react'

interface UserProfile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  full_name?: string
  avatar_url?: string
  role?: string
}

interface CareGroupListItem {
  id: string
  name: string | null
  description: string | null
  avatar_url: string | null
  cover_image_url: string | null
  last_activity_at: string | null
  privacy_setting: string | null
  privacy_level: 'public' | 'private'
  member_count: number
  role_in_group: string | null
  care_recipient_name: string
  care_recipient_avatar_url: string | null
  is_member: boolean
  unread_messages_count?: number
  total_count?: number
  category_name?: string | null
  created_at: string
}

export default function Dashboard() {
  const navigate = useNavigate()
  const [user, setUser] = useState<UserProfile | null>(null)
  const [careGroups, setCareGroups] = useState<CareGroupListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [groupFilter, setGroupFilter] = useState('all')
  const [dashboardStats, setDashboardStats] = useState({
    upcomingAppointments: 0,
    unreadMessages: 0,
    careGroupsCount: 0,
    savedProviders: 0
  })
  const [appointments, setAppointments] = useState<any[]>([])
  const [appointmentsLoading, setAppointmentsLoading] = useState(false)
  const [messages, setMessages] = useState<any[]>([])
  const [messagesLoading, setMessagesLoading] = useState(false)
  const [providers, setProviders] = useState<any[]>([])
  const [providersLoading, setProvidersLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [statsLoading, setStatsLoading] = useState(false)
  
  // Message form state variables
  const [messageForm, setMessageForm] = useState({
    providerId: '',
    subject: '',
    message: ''
  })
  const [sendingMessage, setSendingMessage] = useState(false)
  const [savingDraft, setSavingDraft] = useState(false)
  
  // Form validation state for healthcare messaging
  const [validationErrors, setValidationErrors] = useState({
    providerId: '',
    subject: '',
    message: ''
  })
  
  // Notification state variables - NO HARDCODED DATA
  const [notifications, setNotifications] = useState<any[]>([])
  const [notificationStats, setNotificationStats] = useState({
    unread: 0,
    today: 0,
    thisWeek: 0
  })
  const [notificationsLoading, setNotificationsLoading] = useState(false)
  
  // Real message history state variables - NO HARDCODED DATA
  const [messageHistory, setMessageHistory] = useState<any[]>([])
  const [messageHistoryLoading, setMessageHistoryLoading] = useState(false)
  const [messageStats, setMessageStats] = useState({
    unreadCount: 0,
    totalConversations: 0,
    responseRate: 0
  })
  const [messageStatsLoading, setMessageStatsLoading] = useState(false)

  useEffect(() => {
    checkUser()
  }, [])

  // Load providers, message history, and statistics when Messages tab is active - NO HARDCODED DATA
  useEffect(() => {
    if (activeTab === 'messages' && user) {
      loadProviders()
      loadMessageHistory(user.id)
      loadMessageStats(user.id)
    }
  }, [activeTab, user])

  const checkUser = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        navigate('/auth')
        return
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single()

      if (profile) {
        setUser({
          id: profile.id,
          email: profile.email || session.user.email || '',
          first_name: profile.first_name,
          last_name: profile.last_name,
          full_name: profile.full_name,
          avatar_url: profile.avatar_url,
          role: profile.role
        })
        
        // Load Care Groups data - REAL DATABASE QUERIES FROM ORIGINAL APP
        await loadCareGroupsData(session.user.id)
        // Load Dashboard Statistics - REAL DATABASE QUERIES
        await loadDashboardStats(session.user.id)
        // Load Appointments - REAL DATABASE QUERIES
        await loadAppointments(session.user.id)
        // Load Messages and Providers - REAL DATABASE QUERIES
        await loadMessages(session.user.id)
        await loadProviders()
        // Load Notifications - REAL DATABASE QUERIES
        await loadNotifications(session.user.id)
      }
    } catch (error) {
      console.error('Error checking user:', error)
      navigate('/auth')
    } finally {
      setLoading(false)
    }
  }

  // Real Care Groups data loading from original UserDashboard.tsx - NO MOCKUP
  const loadCareGroupsData = async (userId: string) => {
    try {
      // Get care groups memberships (same as original app)
      const { data: memberships } = await supabase
        .from('care_group_members')
        .select('group_id')
        .eq('user_id', userId)
        .not('group_id', 'is', null)
      
      if (memberships && memberships.length > 0) {
        const groupIds = memberships.map(m => m.group_id)
        const { data: groupsData } = await supabase
          .from('care_groups')
          .select('*')
          .in('id', groupIds)
        
        if (groupsData) {
          setCareGroups(groupsData as CareGroupListItem[])
          // Update care groups count in dashboard stats
          setDashboardStats(prev => ({
            ...prev,
            careGroupsCount: groupsData.length
          }))
        }
      }
    } catch (error) {
      console.error('Error loading care groups data:', error)
    }
  }

  // Load real dashboard statistics from database - NO HARDCODED DATA
  const loadDashboardStats = async (userId: string) => {
    setStatsLoading(true)
    setError(null)
    try {
      // Get upcoming appointments count
      const { data: appointments } = await supabase
        .from('bookings')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'confirmed')
        .gte('start_time', new Date().toISOString())

      // Get unread messages count from real database
      const { data: messagesData } = await supabase
        .from('messages')
        .select('id')
        .eq('recipient_id', userId)
        .eq('is_read', false)

      const unreadMessages = messagesData?.length || 0

      // Get saved providers count from real database
      const { data: savedProvidersData } = await supabase
        .from('saved_providers')
        .select('id')
        .eq('user_id', userId)

      const savedProviders = savedProvidersData?.length || 0

      setDashboardStats({
        upcomingAppointments: appointments?.length || 0,
        unreadMessages,
        careGroupsCount: careGroups.length,
        savedProviders
      })
    } catch (error) {
      console.error('Error loading dashboard stats:', error)
      setError('Failed to load dashboard statistics. Please try refreshing the page.')
    } finally {
      setStatsLoading(false)
    }
  }

  // Load real appointments from database - NO HARDCODED DATA
  const loadAppointments = async (userId: string) => {
    try {
      setAppointmentsLoading(true)
      const { data: appointmentsData } = await supabase
        .from('bookings')
        .select(`
          id,
          start_time,
          end_time,
          service_type,
          status,
          location,
          provider_id,
          profiles!bookings_provider_id_fkey (
            first_name,
            last_name,
            provider_type
          )
        `)
        .eq('user_id', userId)
        .gte('start_time', new Date().toISOString())
        .order('start_time', { ascending: true })
        .limit(10)

      setAppointments(appointmentsData || [])
    } catch (error) {
      console.error('Error loading appointments:', error)
      setAppointments([])
    } finally {
      setAppointmentsLoading(false)
    }
  }

  // Load real messages from database - NO HARDCODED DATA
  const loadMessages = async (userId: string) => {
    try {
      setMessagesLoading(true)
      // For now, messages table might not exist, so we'll show empty state
      // TODO: Implement when messages table is ready
      setMessages([])
    } catch (error) {
      console.error('Error loading messages:', error)
      setMessages([])
    } finally {
      setMessagesLoading(false)
    }
  }

  // Load real providers from database - NO HARDCODED DATA
  const loadProviders = async () => {
    try {
      setProvidersLoading(true)
      const { data: providersData } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, provider_type, role')
        .in('role', ['caregiver', 'companion', 'professional', 'care_checker'])
        .limit(20)

      setProviders(providersData || [])
    } catch (error) {
      console.error('Error loading providers:', error)
      setProviders([])
    } finally {
      setProvidersLoading(false)
    }
  }
  
  // Load real message history from database - NO HARDCODED DATA
  // Safe fallback handling for missing messages table
  const loadMessageHistory = async (userId: string) => {
    try {
      setMessageHistoryLoading(true)
      setError(null)
      
      // Check if messages table exists by attempting a simple query first
      const { data: tableCheck, error: tableError } = await supabase
        .from('messages')
        .select('id')
        .limit(1)
      
      if (tableError) {
        // Messages table doesn't exist - safe fallback to empty state
        console.warn('Messages table not found in database, using empty state:', tableError.message)
        setMessageHistory([])
        setMessageHistoryLoading(false)
        return
      }
      
      // Messages table exists - proceed with full query
      const { data: conversationsData, error: queryError } = await supabase
        .from('messages')
        .select(`
          id,
          sender_id,
          recipient_id,
          subject,
          content,
          created_at,
          read_at,
          sender:profiles!messages_sender_id_fkey(
            id,
            first_name,
            last_name,
            provider_type,
            role
          ),
          recipient:profiles!messages_recipient_id_fkey(
            id,
            first_name,
            last_name,
            provider_type,
            role
          )
        `)
        .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
        .order('created_at', { ascending: false })
        .limit(20)
      
      if (queryError) {
        console.warn('Messages query failed, using empty state:', queryError.message)
        setMessageHistory([])
      } else {
        setMessageHistory(conversationsData || [])
      }
    } catch (error) {
      console.error('Error loading message history (safe fallback):', error)
      setMessageHistory([])
      // Don't set error state for missing table to prevent UI disruption
    } finally {
      setMessageHistoryLoading(false)
    }
  }
  
  // Load real message statistics from database - NO HARDCODED DATA
  // Safe fallback handling for missing messages table
  const loadMessageStats = async (userId: string) => {
    try {
      setMessageStatsLoading(true)
      setError(null)
      
      // Check if messages table exists by attempting a simple query first
      const { data: tableCheck, error: tableError } = await supabase
        .from('messages')
        .select('id')
        .limit(1)
      
      if (tableError) {
        // Messages table doesn't exist - safe fallback to zero stats
        console.warn('Messages table not found for statistics, using zero state:', tableError.message)
        setMessageStats({
          unreadCount: 0,
          totalConversations: 0,
          responseRate: 0
        })
        setMessageStatsLoading(false)
        return
      }
      
      // Messages table exists - proceed with statistics queries
      // Get unread messages count
      const { count: unreadCount, error: unreadError } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('recipient_id', userId)
        .is('read_at', null)
      
      // Get total conversations count (unique conversation partners)
      const { data: conversationsData, error: conversationsError } = await supabase
        .from('messages')
        .select('sender_id, recipient_id')
        .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
      
      // Calculate unique conversation partners
      const uniquePartners = new Set()
      if (!conversationsError && conversationsData) {
        conversationsData.forEach(msg => {
          const partnerId = msg.sender_id === userId ? msg.recipient_id : msg.sender_id
          uniquePartners.add(partnerId)
        })
      }
      
      // Calculate response rate (messages sent vs received)
      const { count: sentCount, error: sentError } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('sender_id', userId)
      
      const { count: receivedCount, error: receivedError } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('recipient_id', userId)
      
      const responseRate = receivedCount && receivedCount > 0 
        ? Math.round(((sentCount || 0) / receivedCount) * 100)
        : 0
      
      // Set statistics with safe fallbacks for any failed queries
      setMessageStats({
        unreadCount: unreadError ? 0 : (unreadCount || 0),
        totalConversations: conversationsError ? 0 : uniquePartners.size,
        responseRate: (sentError || receivedError) ? 0 : responseRate
      })
    } catch (error) {
      console.error('Error loading message statistics (safe fallback):', error)
      setMessageStats({
        unreadCount: 0,
        totalConversations: 0,
        responseRate: 0
      })
      // Don't set error state for missing table to prevent UI disruption
    } finally {
      setMessageStatsLoading(false)
    }
  }
  
  // Comprehensive healthcare message validation functions
  const validateMessageField = (field: string, value: string): string => {
    switch (field) {
      case 'providerId':
        if (!value) return 'Please select a healthcare provider'
        return ''
      
      case 'subject':
        if (!value.trim()) return 'Subject is required for healthcare communications'
        if (value.length < 5) return 'Subject must be at least 5 characters for clarity'
        if (value.length > 100) return 'Subject must be 100 characters or less'
        return ''
      
      case 'message':
        if (!value.trim()) return 'Message content is required'
        if (value.length < 10) return 'Message must be at least 10 characters for meaningful healthcare communication'
        if (value.length > 2000) return 'Message must be 2000 characters or less'
        return ''
      
      default:
        return ''
    }
  }
  
  const validateForm = (): boolean => {
    const errors = {
      providerId: validateMessageField('providerId', messageForm.providerId),
      subject: validateMessageField('subject', messageForm.subject),
      message: validateMessageField('message', messageForm.message)
    }
    
    setValidationErrors(errors)
    
    // Return true if no errors
    return !errors.providerId && !errors.subject && !errors.message
  }
  
  const handleFieldChange = (field: string, value: string) => {
    // Update form state
    setMessageForm(prev => ({ ...prev, [field]: value }))
    
    // Real-time validation with debouncing for better UX
    setTimeout(() => {
      const error = validateMessageField(field, value)
      setValidationErrors(prev => ({ ...prev, [field]: error }))
    }, 300)
  }
  
  // Send message functionality - NO HARDCODED DATA
  // Safe fallback handling for missing messages table
  const sendMessage = async () => {
    // Use comprehensive healthcare validation
    if (!validateForm()) {
      setError('Please fix the validation errors before sending')
      return
    }
    
    try {
      setSendingMessage(true)
      setError(null)
      
      // Check if messages table exists by attempting a simple query first
      const { data: tableCheck, error: tableError } = await supabase
        .from('messages')
        .select('id')
        .limit(1)
      
      if (tableError) {
        // Messages table doesn't exist - provide user feedback and safe fallback
        console.warn('Messages table not found, cannot send message:', tableError.message)
        setError('Message functionality is being set up. Please try again later.')
        setSendingMessage(false)
        return
      }
      
      // Messages table exists - proceed with message sending
      const { data: messageData, error: messageError } = await supabase
        .from('messages')
        .insert({
          sender_id: user?.id,
          recipient_id: messageForm.providerId,
          subject: messageForm.subject,
          content: messageForm.message,
          created_at: new Date().toISOString(),
          read_at: null
        })
        .select()
        .single()
      
      if (messageError) {
        console.error('Error inserting message:', messageError)
        setError('Failed to send message. Please check your information and try again.')
        return
      }
      
      // Clear form after successful send
      setMessageForm({ providerId: '', subject: '', message: '' })
      setValidationErrors({ providerId: '', subject: '', message: '' })
      
      // Show success feedback
      console.log('Message sent successfully:', messageData)
      setError(null)
      
      // Optional: Reload message history to show the sent message
      if (user?.id) {
        loadMessageHistory(user.id)
        loadMessageStats(user.id)
      }
      
    } catch (error) {
      console.error('Error sending message (safe fallback):', error)
      setError('Unable to send message at this time. Please try again later.')
    } finally {
      setSendingMessage(false)
    }
  }
  
  // Save draft functionality - NO HARDCODED DATA  
  const saveDraft = async () => {
    // More lenient validation for drafts - allow partial completion
    if (!messageForm.subject.trim() && !messageForm.message.trim()) {
      setError('Please enter a subject or message content to save as draft')
      return
    }
    
    // Check for character limits even in drafts
    if (messageForm.subject.length > 100) {
      setError('Subject must be 100 characters or less, even for drafts')
      return
    }
    
    if (messageForm.message.length > 2000) {
      setError('Message must be 2000 characters or less, even for drafts')
      return
    }
    
    try {
      setSavingDraft(true)
      setError(null)
      
      const { data: draftData, error: draftError } = await supabase
        .from('message_drafts')
        .insert({
          user_id: user?.id,
          recipient_id: messageForm.providerId || null,
          subject: messageForm.subject,
          content: messageForm.message,
          created_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (draftError) throw draftError
      
      // Show success feedback
      console.log('Draft saved successfully:', draftData)
      
    } catch (error) {
      console.error('Error saving draft:', error)
      setError('Failed to save draft. Please try again.')
    } finally {
      setSavingDraft(false)
    }
  }

  // Load real notifications from database - NO HARDCODED DATA
  const loadNotifications = async (userId: string) => {
    try {
      setNotificationsLoading(true)
      
      // Safe fallback for missing notifications table
      const { data: tableCheck, error: tableError } = await supabase
        .from('notifications')
        .select('id')
        .limit(1)
      
      if (tableError) {
        console.log('Notifications table not available, using empty state')
        setNotifications([])
        setNotificationStats({ unread: 0, today: 0, thisWeek: 0 })
        setNotificationsLoading(false)
        return
      }
      
      // Load recent notifications from database
      const { data: notificationsData } = await supabase
        .from('notifications')
        .select(`
          id,
          type,
          title,
          message,
          created_at,
          read_at,
          metadata
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10)
      
      setNotifications(notificationsData || [])
      
      // Calculate notification statistics from real data
      const now = new Date()
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      
      const unreadCount = (notificationsData || []).filter(n => !n.read_at).length
      const todayCount = (notificationsData || []).filter(n => new Date(n.created_at) >= todayStart).length
      const weekCount = (notificationsData || []).filter(n => new Date(n.created_at) >= weekStart).length
      
      setNotificationStats({
        unread: unreadCount,
        today: todayCount,
        thisWeek: weekCount
      })
      
    } catch (error) {
      console.error('Error loading notifications:', error)
      setNotifications([])
      setNotificationStats({ unread: 0, today: 0, thisWeek: 0 })
    } finally {
      setNotificationsLoading(false)
    }
  }

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      navigate('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const sidebarItems = [
    { id: 'overview', name: 'Overview', icon: User },
    { id: 'appointments', name: 'Appointments', icon: Calendar },
    { id: 'messages', name: 'Messages', icon: MessageSquare },
    { id: 'care-groups', name: 'Care Groups', icon: Heart },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'settings', name: 'Settings', icon: Settings },
  ]

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="w-16 h-16 rounded-full border-3 border-transparent animate-spin"
                   style={{ borderTopColor: 'var(--primary)', borderRightColor: 'var(--primary)' }}></div>
              <div className="absolute inset-0 w-16 h-16 rounded-full border-3 opacity-20"
                   style={{ borderColor: 'var(--primary)' }}></div>
            </div>
          </div>
          <h2 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
            Loading Dashboard
          </h2>
          <p className="text-base" style={{ color: 'var(--text-secondary)' }}>
            Preparing your personalized care management center...
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex" style={{ minHeight: 'calc(100vh - 80px)' }}>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div
            className="fixed inset-0"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            onClick={() => setSidebarOpen(false)}
          />
        </div>
      )}

      {/* Sidebar */}
      <div className={`w-64 flex-shrink-0 shadow-lg transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 border-r`} style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
        <div className="flex items-center justify-between h-16 px-6" style={{ borderBottom: '1px solid var(--border-light)' }}>
          <div className="flex items-center space-x-3">
            <span className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>Dashboard</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="sidebar-button lg:hidden p-1 rounded-md transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <nav className="mt-8 px-4">
          <div className="space-y-2">
            {sidebarItems.map((item) => {
              const Icon = item.icon
              return (
                <button
                  key={item.id}
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    setActiveTab(item.id)
                    setSidebarOpen(false)
                  }}
                  className={`macos-sidebar-item w-full flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 hover:translate-x-0.5 ${
                    activeTab === item.id ? 'active font-semibold' : 'font-medium'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-4" />
                  {item.name}
                </button>
              )
            })}
          </div>

          <div className="mt-8 pt-6" style={{ borderTop: '1px solid var(--border-light)' }}>
            <button
              onClick={handleSignOut}
              className="sidebar-button w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors"
            >
              <LogOut className="w-5 h-5 mr-3" />
              Sign Out
            </button>
          </div>
        </nav>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <div className="shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
          <div className="flex items-center justify-between h-16 px-8">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-lg transition-colors"
              style={{ color: 'var(--text-secondary)' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            >
              <Menu className="w-6 h-6" />
            </button>

            <h1 className="text-3xl font-light capitalize tracking-tight" style={{ color: 'var(--text-primary)', fontWeight: '300', letterSpacing: '-0.01em' }}>
              {activeTab.replace('-', ' ')}
            </h1>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                {user?.avatar_url ? (
                  <img
                    src={user.avatar_url}
                    alt="Profile"
                    className="w-8 h-8 rounded-full"
                  />
                ) : (
                  <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--primary)' }}>
                    <span className="text-sm font-medium" style={{ color: 'var(--bg-primary)' }}>
                      {user?.first_name?.[0]}{user?.last_name?.[0]}
                    </span>
                  </div>
                )}
                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                  {user?.first_name} {user?.last_name}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard content */}
        <div className="flex-1 overflow-y-auto p-8">
          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 rounded-lg border" style={{ backgroundColor: 'var(--bg-secondary)', borderColor: 'var(--border-medium)', color: 'var(--text-primary)' }}>
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--primary)' }}>
                  <span className="text-xs font-bold" style={{ color: 'var(--bg-primary)' }}>!</span>
                </div>
                <span className="text-sm font-medium">{error}</span>
                <button
                  onClick={() => setError(null)}
                  className="ml-auto text-sm transition-colors"
                  style={{ color: 'var(--text-secondary)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                >
                  Dismiss
                </button>
              </div>
            </div>
          )}

          {activeTab === 'overview' && (
            <div className="space-y-12">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div className="dashboard-card rounded-2xl p-8 border transition-all duration-300 cursor-pointer hover:-translate-y-1" style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05)' }} onMouseEnter={(e) => e.currentTarget.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)'} onMouseLeave={(e) => e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05)'}>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-2xl flex items-center justify-center"
                         style={{ backgroundColor: 'var(--bg-accent)' }}>
                      <Calendar className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    </div>
                    <div className="ml-4">
                      <p className="text-xs font-semibold uppercase tracking-wider" style={{ color: 'var(--text-secondary)', letterSpacing: '0.08em', lineHeight: '1.4' }}>Upcoming Appointments</p>
                      <p className="text-5xl font-black" style={{ color: 'var(--text-primary)', lineHeight: '1', letterSpacing: '-0.02em' }}>{dashboardStats.upcomingAppointments}</p>
                    </div>
                  </div>
                </div>

                <div className="dashboard-card rounded-2xl p-8 border transition-all duration-300 cursor-pointer hover:-translate-y-1" style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05)' }} onMouseEnter={(e) => e.currentTarget.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)'} onMouseLeave={(e) => e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05)'}>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-2xl flex items-center justify-center"
                         style={{ backgroundColor: 'var(--bg-accent)' }}>
                      <MessageSquare className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    </div>
                    <div className="ml-4">
                      <p className="text-xs font-semibold uppercase tracking-wider" style={{ color: 'var(--text-secondary)', letterSpacing: '0.08em', lineHeight: '1.4' }}>Unread Messages</p>
                      <p className="text-5xl font-black" style={{ color: 'var(--text-primary)', lineHeight: '1', letterSpacing: '-0.02em' }}>{dashboardStats.unreadMessages}</p>
                    </div>
                  </div>
                </div>

                <div className="dashboard-card rounded-2xl p-8 border transition-all duration-300 cursor-pointer hover:-translate-y-1" style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05)' }} onMouseEnter={(e) => e.currentTarget.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)'} onMouseLeave={(e) => e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05)'}>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-2xl flex items-center justify-center"
                         style={{ backgroundColor: 'var(--bg-accent)' }}>
                      <Heart className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    </div>
                    <div className="ml-4">
                      <p className="text-xs font-semibold uppercase tracking-wider" style={{ color: 'var(--text-secondary)', letterSpacing: '0.08em', lineHeight: '1.4' }}>Care Groups</p>
                      <p className="text-5xl font-black" style={{ color: 'var(--text-primary)', lineHeight: '1', letterSpacing: '-0.02em' }}>{dashboardStats.careGroupsCount}</p>
                    </div>
                  </div>
                </div>

                <div className="dashboard-card rounded-2xl p-8 border transition-all duration-300 cursor-pointer hover:-translate-y-1" style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05)' }} onMouseEnter={(e) => e.currentTarget.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)'} onMouseLeave={(e) => e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05)'}>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-2xl flex items-center justify-center"
                         style={{ backgroundColor: 'var(--bg-accent)' }}>
                      <Star className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    </div>
                    <div className="ml-4">
                      <p className="text-xs font-semibold uppercase tracking-wider" style={{ color: 'var(--text-secondary)', letterSpacing: '0.08em', lineHeight: '1.4' }}>Saved Providers</p>
                      <p className="text-5xl font-black" style={{ color: 'var(--text-primary)', lineHeight: '1', letterSpacing: '-0.02em' }}>{dashboardStats.savedProviders}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="p-8 rounded-lg shadow-sm border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <h3 className="text-lg font-medium mb-6" style={{ color: 'var(--text-primary)' }}>Recent Appointments</h3>
                  <div className="space-y-6">
                    {appointmentsLoading ? (
                      <div className="text-center py-4">
                        <div className="inline-flex items-center gap-2" style={{ color: 'var(--text-secondary)' }}>
                          <div className="w-4 h-4 border-2 border-transparent rounded-full animate-spin" style={{ borderTopColor: 'var(--primary)' }}></div>
                          Loading appointments...
                        </div>
                      </div>
                    ) : appointments.length > 0 ? (
                      appointments.slice(0, 2).map((appointment, index) => (
                        <div key={index} className="flex items-center space-x-4">
                          <div className="w-10 h-10 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--primary)' }}>
                            <span className="text-sm font-medium" style={{ color: 'var(--bg-primary)' }}>
                              {appointment.provider_name ? appointment.provider_name.split(' ').map((n: string) => n[0]).join('') : 'AP'}
                            </span>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {appointment.provider_name || 'Healthcare Provider'}
                            </p>
                            <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              {appointment.service_type || 'Appointment'} • {new Date(appointment.start_time).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex items-center text-sm" style={{ color: 'var(--text-secondary)' }}>
                            <Clock className="w-4 h-4 mr-1" />
                            {appointment.duration || '1 hour'}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4" style={{ color: 'var(--text-secondary)' }}>
                        No upcoming appointments
                      </div>
                    )}
                  </div>
                </div>

                <div className="p-6 rounded-lg shadow-sm border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <h3 className="text-lg font-medium mb-4" style={{ color: 'var(--text-primary)' }}>Recent Messages</h3>
                  <div className="space-y-4">
                    {messagesLoading ? (
                      <div className="text-center py-4">
                        <div className="inline-flex items-center gap-2" style={{ color: 'var(--text-secondary)' }}>
                          <div className="w-4 h-4 border-2 border-transparent rounded-full animate-spin" style={{ borderTopColor: 'var(--primary)' }}></div>
                          Loading messages...
                        </div>
                      </div>
                    ) : messages.length > 0 ? (
                      messages.slice(0, 2).map((message, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--primary)' }}>
                            <span className="text-xs font-medium" style={{ color: 'var(--bg-primary)' }}>
                              {message.sender_name ? message.sender_name.split(' ').map((n: string) => n[0]).join('') : 'M'}
                            </span>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {message.sender_name || 'Contact'}
                            </p>
                            <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              {message.content || 'New message'}
                            </p>
                            <p className="text-xs" style={{ color: 'var(--text-muted)' }}>
                              {message.created_at ? new Date(message.created_at).toLocaleDateString() : 'Recently'}
                            </p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4" style={{ color: 'var(--text-secondary)' }}>
                        No recent messages
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'appointments' && (
            <div className="space-y-8">
              {/* Upcoming Appointments */}
              <div className="rounded-xl shadow-sm border"
                   style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="p-8">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>Upcoming Appointments</h3>
                    <button className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                            style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                            onClick={() => navigate('/caregivers')}>
                      <Plus className="w-4 h-4 mr-2 inline" />
                      Book Appointment
                    </button>
                  </div>

                  {appointmentsLoading ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-2 border-transparent"
                           style={{ borderTopColor: 'var(--primary)', borderRightColor: 'var(--primary)' }}></div>
                    </div>
                  ) : appointments.length > 0 ? (
                    <div className="space-y-4">
                      {appointments.map((appointment, index) => (
                        <div key={appointment.id}
                             className="border-l-4 pl-6 py-4 rounded-r-lg transition-all duration-200 hover:shadow-sm"
                             style={{
                               borderLeftColor: 'var(--primary)',
                               backgroundColor: 'var(--bg-secondary)'
                             }}>
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <h4 className="font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>
                                {appointment.service_type} - {appointment.profiles?.first_name} {appointment.profiles?.last_name}
                              </h4>
                              <p className="text-sm mb-1" style={{ color: 'var(--text-secondary)' }}>
                                {new Date(appointment.start_time).toLocaleDateString()} at {new Date(appointment.start_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                              </p>
                              <p className="text-sm" style={{ color: 'var(--text-muted)' }}>
                                📍 {appointment.location || 'Location TBD'}
                              </p>
                            </div>
                            <div className="flex space-x-3">
                              <button className="px-4 py-2 text-sm font-medium rounded-lg transition-colors"
                                      style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                                      onClick={() => navigate(`/appointment-details/${appointment.id}`)}>
                                View Details
                              </button>
                              <button className="px-4 py-2 text-sm font-medium rounded-lg border transition-colors"
                                      style={{ borderColor: 'var(--border-medium)', color: 'var(--text-secondary)' }}
                                      onClick={() => navigate(`/reschedule-appointment/${appointment.id}`)}>
                                Reschedule
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Calendar className="w-12 h-12 mx-auto mb-4" style={{ color: 'var(--text-muted)' }} />
                      <h4 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>No Upcoming Appointments</h4>
                      <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>You don't have any appointments scheduled yet.</p>
                      <button className="px-6 py-3 rounded-lg font-medium transition-colors"
                              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                              onClick={() => navigate('/caregivers')}>
                        Book Your First Appointment
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="rounded-2xl shadow-sm border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="p-6">
                  <h3 className="text-lg font-medium mb-4" style={{ color: 'var(--text-primary)' }}>Quick Actions</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                      className="flex items-center justify-center p-4 border rounded-xl transition-colors"
                      style={{ borderColor: 'var(--border-medium)', color: 'var(--text-primary)' }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      onClick={() => navigate('/caregivers')}
                    >
                      <Calendar className="w-5 h-5 mr-2" style={{ color: 'var(--primary)' }} />
                      <span className="text-sm font-medium">Book New Appointment</span>
                    </button>
                    <button
                      className="flex items-center justify-center p-4 border rounded-xl transition-colors"
                      style={{ borderColor: 'var(--border-medium)', color: 'var(--text-primary)' }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      onClick={() => setActiveTab('appointments')}
                    >
                      <Clock className="w-5 h-5 mr-2" style={{ color: 'var(--primary)' }} />
                      <span className="text-sm font-medium">View Calendar</span>
                    </button>
                    <button
                      className="flex items-center justify-center p-4 border rounded-xl transition-colors"
                      style={{ borderColor: 'var(--border-medium)', color: 'var(--text-primary)' }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      onClick={() => setActiveTab('messages')}
                    >
                      <MessageSquare className="w-5 h-5 mr-2" style={{ color: 'var(--primary)' }} />
                      <span className="text-sm font-medium">Contact Provider</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Appointment History */}
              <div className="rounded-2xl shadow-sm border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="p-6">
                  <h3 className="text-lg font-medium mb-4" style={{ color: 'var(--text-primary)' }}>Recent Appointments</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                      <div>
                        <p className="font-medium text-gray-900">Wellness Check - Robert Kim</p>
                        <p className="text-sm text-gray-600">Completed - Last Monday</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          ✓ Completed
                        </span>
                        <button className="text-sm text-logo-green hover:text-green-600">
                          View Notes
                        </button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                      <div>
                        <p className="font-medium text-gray-900">Checkup - Sarah Martinez</p>
                        <p className="text-sm text-gray-600">Completed - 2 weeks ago</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          ✓ Completed
                        </span>
                        <button className="text-sm text-logo-green hover:text-green-600">
                          View Notes
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'messages' && (
            <div className="space-y-8">
              {/* Message Composition */}
              <div className="rounded-xl shadow-sm border"
                   style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="p-8">
                  <div className="flex items-center justify-between mb-7">
                    <h3 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>New Message</h3>
                    <button className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                            style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                            onClick={() => navigate('/messages')}>
                      <MessageSquare className="w-4 h-4 mr-2 inline" />
                      View All Messages
                    </button>
                  </div>
                  <div className="space-y-7">
                    {/* Error handling UI - Apple style design */}
                    {error && (
                      <div className="flex items-start space-x-3 p-4 rounded-xl border-l-4 transition-all duration-300"
                           style={{
                             backgroundColor: 'var(--bg-secondary)',
                             borderLeftColor: '#ef4444',
                             borderColor: 'var(--border-medium)'
                           }}>
                        <div className="flex-shrink-0 mt-0.5">
                          <svg className="w-5 h-5" style={{ color: '#ef4444' }} fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium" style={{ color: '#dc2626' }}>Error</p>
                          <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>{error}</p>
                        </div>
                        <button 
                          onClick={() => setError(null)}
                          className="flex-shrink-0 ml-4 p-1 rounded-lg transition-colors hover:bg-gray-100"
                          style={{ color: 'var(--text-secondary)' }}
                          aria-label="Dismiss error">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    )}
                    <div>
                      <label className="block text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>To</label>
                      <select className="w-full px-4 py-3 rounded-lg border transition-colors focus:outline-none focus:ring-2"
                              value={messageForm.providerId}
                              onChange={(e) => handleFieldChange('providerId', e.target.value)}
                              style={{
                                borderColor: 'var(--border-medium)',
                                backgroundColor: 'var(--bg-primary)',
                                color: 'var(--text-primary)',
                                focusRingColor: 'var(--focus-shadow)'
                              }}>
                        <option value="">Select a provider...</option>
                        {providersLoading ? (
                          <option disabled>Loading providers...</option>
                        ) : providers.length > 0 ? (
                          providers.map((provider) => (
                            <option key={provider.id} value={provider.id}>
                              {provider.first_name} {provider.last_name} - {provider.provider_type || provider.role}
                            </option>
                          ))
                        ) : (
                          <option disabled>No providers available</option>
                        )}
                      </select>
                      {validationErrors.providerId && (
                        <p className="text-sm mt-2 px-1" style={{ color: '#dc2626' }}>
                          {validationErrors.providerId}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>Subject</label>
                      <input
                        type="text"
                        value={messageForm.subject}
                        onChange={(e) => handleFieldChange('subject', e.target.value)}
                        placeholder="Subject (e.g., Appointment follow-up, Care questions, Medication inquiry)"
                        className="w-full px-4 py-3 rounded-lg border transition-colors focus:outline-none focus:ring-2"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          focusRingColor: 'var(--focus-shadow)'
                        }}
                      />
                      {validationErrors.subject && (
                        <p className="text-sm mt-2 px-1" style={{ color: '#dc2626' }}>
                          {validationErrors.subject}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>Message</label>
                      <textarea
                        rows={5}
                        value={messageForm.message}
                        onChange={(e) => handleFieldChange('message', e.target.value)}
                        placeholder="Describe your care needs, questions about treatment, or appointment concerns..."
                        className="w-full px-4 py-3 rounded-lg border transition-colors focus:outline-none focus:ring-2 resize-none"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          focusRingColor: 'var(--focus-shadow)'
                        }}
                      />
                      {validationErrors.message && (
                        <p className="text-sm mt-2 px-1" style={{ color: '#dc2626' }}>
                          {validationErrors.message}
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-6">
                      <button className="px-6 py-3 rounded-xl font-medium transition-all duration-200 flex items-center shadow-sm hover:shadow-md"
                              onClick={sendMessage}
                              disabled={sendingMessage || savingDraft}
                              style={{ 
                                backgroundColor: sendingMessage ? 'var(--border-medium)' : 'var(--primary)', 
                                color: 'var(--bg-primary)',
                                opacity: sendingMessage || savingDraft ? 0.6 : 1,
                                cursor: sendingMessage || savingDraft ? 'not-allowed' : 'pointer',
                                boxShadow: sendingMessage || savingDraft ? 'none' : '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
                                transform: 'translateY(0)',
                                fontWeight: '600'
                              }}
                              onMouseEnter={(e) => {
                                if (!sendingMessage && !savingDraft) {
                                  e.currentTarget.style.transform = 'translateY(-1px)';
                                  e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)';
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!sendingMessage && !savingDraft) {
                                  e.currentTarget.style.transform = 'translateY(0)';
                                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)';
                                }
                              }}>
                        <MessageSquare className="w-4 h-4 mr-2" />
                        {sendingMessage ? 'Sending...' : 'Send Message'}
                      </button>
                      <button className="px-6 py-3 rounded-xl border font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                              onClick={saveDraft}
                              disabled={sendingMessage || savingDraft}
                              style={{ 
                                borderColor: 'var(--border-medium)', 
                                color: savingDraft ? 'var(--border-medium)' : 'var(--text-secondary)',
                                opacity: sendingMessage || savingDraft ? 0.6 : 1,
                                cursor: sendingMessage || savingDraft ? 'not-allowed' : 'pointer',
                                backgroundColor: 'transparent',
                                boxShadow: sendingMessage || savingDraft ? 'none' : '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
                                transform: 'translateY(0)',
                                fontWeight: '600'
                              }}
                              onMouseEnter={(e) => {
                                if (!sendingMessage && !savingDraft) {
                                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                                  e.currentTarget.style.transform = 'translateY(-1px)';
                                  e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)';
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!sendingMessage && !savingDraft) {
                                  e.currentTarget.style.backgroundColor = 'transparent';
                                  e.currentTarget.style.transform = 'translateY(0)';
                                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)';
                                }
                              }}>
                        {savingDraft ? 'Saving...' : 'Save Draft'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Conversations */}
              <div className="rounded-xl shadow-sm border"
                   style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="p-8">
                  <h3 className="text-xl font-semibold mb-6" style={{ color: 'var(--text-primary)' }}>Recent Conversations</h3>

                  {messagesLoading ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-2 border-transparent"
                           style={{ borderTopColor: 'var(--primary)', borderRightColor: 'var(--primary)' }}></div>
                    </div>
                  ) : messages.length > 0 ? (
                    <div className="space-y-4">
                      {messages.map((message, index) => (
                        <div key={message.id}
                             className="flex items-start space-x-4 p-4 rounded-lg border transition-all duration-200 hover:shadow-sm cursor-pointer"
                             style={{
                               backgroundColor: 'var(--bg-secondary)',
                               borderColor: 'var(--border-light)'
                             }}>
                          <div className="w-12 h-12 rounded-full flex items-center justify-center"
                               style={{ backgroundColor: 'var(--primary)' }}>
                            <span className="text-sm font-medium" style={{ color: 'var(--bg-primary)' }}>
                              {message.sender_name?.charAt(0) || 'U'}
                            </span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="text-sm font-semibold" style={{ color: 'var(--text-primary)' }}>
                                {message.sender_name || 'Unknown User'}
                              </h4>
                              <span className="text-xs" style={{ color: 'var(--text-muted)' }}>
                                {new Date(message.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            <p className="text-sm mb-1" style={{ color: 'var(--text-secondary)' }}>
                              {message.subject || 'No subject'}
                            </p>
                            <p className="text-sm truncate" style={{ color: 'var(--text-muted)' }}>
                              {message.content || 'No preview available'}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <MessageSquare className="w-12 h-12 mx-auto mb-4" style={{ color: 'var(--text-muted)' }} />
                      <h4 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>No Messages Yet</h4>
                      <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>Start a conversation with your care providers.</p>
                      <button className="px-6 py-3 rounded-lg font-medium transition-colors"
                              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                        Send Your First Message
                      </button>
                    </div>
                  )}
                  {/* Real message history from database - NO HARDCODED DATA */}
                  {messageHistoryLoading ? (
                    <div className="space-y-4">
                      {[...Array(4)].map((_, i) => (
                        <div key={i} className="flex items-start space-x-3 p-3 rounded-lg border border-gray-100 animate-pulse">
                          <div className="w-10 h-10 bg-gray-200 rounded-full" />
                          <div className="flex-1 min-w-0">
                            <div className="h-4 bg-gray-200 rounded w-1/3 mb-2" />
                            <div className="h-3 bg-gray-200 rounded w-2/3 mb-1" />
                            <div className="h-3 bg-gray-200 rounded w-1/4" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : messageHistory && messageHistory.length > 0 ? (
                    <div className="space-y-4">
                      {messageHistory.map((message) => {
                        const isFromUser = message.sender_id === user?.id
                        const partner = isFromUser ? message.recipient : message.sender
                        const partnerName = partner ? `${partner.first_name} ${partner.last_name}` : 'Unknown User'
                        const partnerInitials = partner ? `${partner.first_name?.[0] || ''}${partner.last_name?.[0] || ''}`.toUpperCase() : 'U'
                        const partnerRole = partner?.provider_type || partner?.role || 'User'
                        const isUnread = !message.read_at && !isFromUser
                        
                        return (
                          <div key={message.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-gray-100 transition-colors">
                            <div className="w-10 h-10 rounded-full flex items-center justify-center" 
                                 style={{ backgroundColor: 'var(--primary)' }}>
                              <span className="text-white text-sm font-medium">{partnerInitials}</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  {partnerName}
                                </h4>
                                <span className="text-xs" style={{ color: 'var(--text-muted)' }}>
                                  {new Date(message.created_at).toLocaleDateString()}
                                </span>
                              </div>
                              <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
                                {message.subject || 'No subject'}
                              </p>
                              <p className="text-sm truncate mt-1" style={{ color: 'var(--text-muted)' }}>
                                {message.content ? message.content.substring(0, 60) + (message.content.length > 60 ? '...' : '') : 'No preview available'}
                              </p>
                              <p className="text-xs mt-1" style={{ color: 'var(--text-muted)' }}>
                                {partnerRole}
                              </p>
                            </div>
                            {isUnread && (
                              <div className="flex items-center">
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" 
                                      style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                                  New
                                </span>
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <MessageSquare className="w-8 h-8 mx-auto mb-3" style={{ color: 'var(--text-muted)' }} />
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>No message history found</p>
                      <p className="text-xs mt-1" style={{ color: 'var(--text-muted)' }}>Send your first message to start a conversation</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Message Stats & Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Real Message Statistics from database - NO HARDCODED DATA */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Message Statistics</h3>
                    {messageStatsLoading ? (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Unread Messages</span>
                          <div className="h-6 w-8 bg-gray-200 rounded animate-pulse" />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Total Conversations</span>
                          <div className="h-6 w-8 bg-gray-200 rounded animate-pulse" />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Response Rate</span>
                          <div className="h-6 w-12 bg-gray-200 rounded animate-pulse" />
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Unread Messages</span>
                          <span className="text-lg font-semibold" style={{ color: 'var(--primary)' }}>
                            {messageStats.unreadCount}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Total Conversations</span>
                          <span className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
                            {messageStats.totalConversations}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Response Rate</span>
                          <span className="text-lg font-semibold" style={{ color: 'var(--primary)' }}>
                            {messageStats.responseRate}%
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Quick Message Actions */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                    <div className="space-y-3">
                      <button className="w-full flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <MessageSquare className="w-5 h-5 text-logo-green mr-3" />
                        <span className="text-sm font-medium">Mark All as Read</span>
                      </button>
                      <button className="w-full flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <Settings className="w-5 h-5 text-gray-500 mr-3" />
                        <span className="text-sm font-medium">Message Preferences</span>
                      </button>
                      <button className="w-full flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <Bell className="w-5 h-5 text-blue-500 mr-3" />
                        <span className="text-sm font-medium">Notification Settings</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'care-groups' && (
            <div className="space-y-8">
              {/* Care Groups Header */}
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-semibold tracking-tight" style={{ color: 'var(--text-primary)' }}>Your Care Groups</h2>
                <button className="px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
                        style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                  <Heart className="w-4 h-4" />
                  <span>Create Care Group</span>
                </button>
              </div>

              {/* Search and Filter Controls */}
              <div className="rounded-xl shadow-sm border p-8"
                   style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Search Bar */}
                  <div className="md:col-span-2">
                    <div className="relative">
                      <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4"
                              style={{ color: 'var(--text-muted)' }} />
                      <input
                        type="search"
                        placeholder="Search care groups by name, condition, or location..."
                        className="w-full pl-12 pr-4 py-3 rounded-lg border transition-colors focus:outline-none focus:ring-2"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          focusRingColor: 'var(--focus-shadow)'
                        }}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                  </div>
                  
                  {/* Filter Dropdown */}
                  <div>
                    <select
                      className="w-full px-4 py-3 rounded-lg border transition-colors focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--border-medium)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)',
                        focusRingColor: 'var(--focus-shadow)'
                      }}
                      value={groupFilter}
                      onChange={(e) => setGroupFilter(e.target.value)}
                    >
                      <option value="all">All Groups</option>
                      <option value="my-groups">My Groups</option>
                      <option value="public">Public Groups</option>
                      <option value="recent">Recently Active</option>
                    </select>
                  </div>
                </div>
                
                {/* Quick Action Buttons */}
                <div className="flex items-center space-x-3 mt-4 pt-4 border-t border-gray-200">
                  <button className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                    <Users className="w-4 h-4 mr-2" />
                    Find Groups to Join
                  </button>
                  <button className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Active Discussions
                  </button>
                  <button className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                    <Calendar className="w-4 h-4 mr-2" />
                    Upcoming Events
                  </button>
                </div>
              </div>

              {/* Care Groups List */}
              {careGroups && careGroups.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {careGroups.map((careGroup) => (
                    <div key={careGroup.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900 mb-2">{careGroup.name}</h3>
                          <p className="text-sm text-gray-600 mb-3">{careGroup.description || 'No description provided'}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Users className="w-4 h-4 mr-1" />
                              <span>{careGroup.member_count || 0} members</span>
                            </div>
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              <span>Created {new Date(careGroup.created_at).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          {careGroup.privacy_level === 'private' && (
                            <div className="w-2 h-2 bg-red-500 rounded-full" title="Private Group" />
                          )}
                          {careGroup.privacy_level === 'public' && (
                            <div className="w-2 h-2 bg-logo-green rounded-full" title="Public Group" />
                          )}
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        {/* Primary Action Buttons */}
                        <div className="flex space-x-3">
                          <button 
                            onClick={() => navigate(`/care-groups/${careGroup.id}`)}
                            className="flex-1 px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                          >
                            View Details
                          </button>
                          <button 
                            onClick={() => navigate(`/care-groups/${careGroup.id}/settings`)}
                            className="flex-1 px-3 py-2 text-sm bg-logo-green text-white rounded-md hover:bg-green-600 transition-colors"
                          >
                            Manage
                          </button>
                        </div>
                        
                        {/* Secondary Action Buttons */}
                        <div className="grid grid-cols-3 gap-2">
                          <button 
                            onClick={() => navigate(`/care-groups/${careGroup.id}/members`)}
                            className="px-2 py-1 text-xs border border-gray-300 text-gray-600 rounded hover:bg-gray-50 transition-colors flex items-center justify-center"
                          >
                            <Users className="w-3 h-3 mr-1" />
                            Members
                          </button>
                          <button 
                            onClick={() => navigate(`/care-groups/${careGroup.id}/events`)}
                            className="px-2 py-1 text-xs border border-gray-300 text-gray-600 rounded hover:bg-gray-50 transition-colors flex items-center justify-center"
                          >
                            <Calendar className="w-3 h-3 mr-1" />
                            Events
                          </button>
                          {careGroup.is_member ? (
                            <button 
                              className="px-2 py-1 text-xs border border-red-300 text-red-600 rounded hover:bg-red-50 transition-colors flex items-center justify-center"
                              title="Leave Group"
                            >
                              <LogOut className="w-3 h-3 mr-1" />
                              Leave
                            </button>
                          ) : (
                            <button 
                              className="px-2 py-1 text-xs border border-logo-green text-logo-green rounded hover:bg-green-50 transition-colors flex items-center justify-center"
                              title="Join Group"
                            >
                              <Plus className="w-3 h-3 mr-1" />
                              Join
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="rounded-2xl shadow-sm border p-12 text-center" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <Heart className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-muted)' }} />
                  <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>No Care Groups Yet</h3>
                  <p className="mb-6 max-w-md mx-auto" style={{ color: 'var(--text-secondary)' }}>
                    Care groups help you connect with others who share similar caregiving experiences.
                    Create your first care group to get started.
                  </p>
                  <button
                    className="px-6 py-3 rounded-xl transition-colors flex items-center space-x-2 mx-auto"
                    style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                    onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                  >
                    <Heart className="w-5 h-5" />
                    <span>Create Your First Care Group</span>
                  </button>
                </div>
              )}

              {/* Care Groups Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="flex items-center">
                    <Heart className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    <div className="ml-4">
                      <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Total Care Groups</p>
                      <p className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>{careGroups?.length || 0}</p>
                    </div>
                  </div>
                </div>
                
                <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="flex items-center">
                    <Users className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    <div className="ml-4">
                      <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Total Members</p>
                      <p className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                        {careGroups?.reduce((total, group) => total + (group.member_count || 0), 0) || 0}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="flex items-center">
                    <MessageSquare className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    <div className="ml-4">
                      <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Active Discussions</p>
                      <p className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>0</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-8">
              {/* Notification Settings */}
              <div className="rounded-xl shadow-sm border"
                   style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="p-8">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>Notification Preferences</h3>
                    <button className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                            style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                      <Bell className="w-4 h-4 mr-2 inline" />
                      Test Notifications
                    </button>
                  </div>
                  <div className="space-y-6">
                    {/* Email Notifications */}
                    <div className="flex items-center justify-between p-6 rounded-lg border transition-all duration-200"
                         style={{ borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-secondary)' }}>
                      <div>
                        <h4 className="text-base font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Email Notifications</h4>
                        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Receive notifications via email</p>
                      </div>
                      <button className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2"
                              style={{ backgroundColor: 'var(--primary)', focusRingColor: 'var(--focus-shadow)' }}>
                        <span className="inline-block h-4 w-4 translate-x-6 transform rounded-full transition-transform"
                              style={{ backgroundColor: 'var(--bg-primary)' }} />
                      </button>
                    </div>

                    {/* Push Notifications */}
                    <div className="flex items-center justify-between p-6 rounded-lg border transition-all duration-200"
                         style={{ borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-secondary)' }}>
                      <div>
                        <h4 className="text-base font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Push Notifications</h4>
                        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Get instant notifications on your device</p>
                      </div>
                      <button className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2"
                              style={{ backgroundColor: 'var(--primary)', focusRingColor: 'var(--focus-shadow)' }}>
                        <span className="inline-block h-4 w-4 translate-x-6 transform rounded-full transition-transform"
                              style={{ backgroundColor: 'var(--bg-primary)' }} />
                      </button>
                    </div>

                    {/* SMS Notifications */}
                    <div className="flex items-center justify-between p-6 rounded-lg border transition-all duration-200"
                         style={{ borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-secondary)' }}>
                      <div>
                        <h4 className="text-base font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>SMS Notifications</h4>
                        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Receive important updates via text message</p>
                      </div>
                      <button className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2"
                              style={{ backgroundColor: 'var(--border-medium)', focusRingColor: 'var(--focus-shadow)' }}>
                        <span className="inline-block h-4 w-4 translate-x-1 transform rounded-full transition-transform"
                              style={{ backgroundColor: 'var(--bg-primary)' }} />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Notifications */}
              <div className="rounded-xl shadow-sm border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="p-6">
                  <h3 className="text-lg font-medium mb-4" style={{ color: 'var(--text-primary)' }}>Recent Notifications</h3>
                  {notificationsLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-transparent"
                           style={{ borderTopColor: 'var(--primary)', borderRightColor: 'var(--primary)' }}></div>
                    </div>
                  ) : notifications.length > 0 ? (
                    <div className="space-y-4">
                      {notifications.map((notification, index) => {
                        const getIcon = (type: string) => {
                          switch (type) {
                            case 'appointment': return Calendar
                            case 'message': return MessageSquare
                            case 'care_group': return Users
                            case 'task': return CheckCircle
                            default: return Bell
                          }
                        }
                        
                        const Icon = getIcon(notification.type)
                        const isUnread = !notification.read_at
                        const timeAgo = new Date(notification.created_at).toLocaleString()
                        
                        return (
                          <div key={notification.id}
                               className="flex items-start space-x-3 p-3 rounded-lg border transition-all duration-200"
                               style={{ 
                                 backgroundColor: isUnread ? 'var(--bg-accent)' : 'var(--bg-secondary)', 
                                 borderColor: 'var(--border-light)' 
                               }}
                               onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'}
                               onMouseLeave={(e) => e.currentTarget.style.backgroundColor = isUnread ? 'var(--bg-accent)' : 'var(--bg-secondary)'}>
                            <div className="flex-shrink-0">
                              <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--primary)' }}>
                                <Icon className="w-4 h-4" style={{ color: 'var(--bg-primary)' }} />
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>{notification.title}</h4>
                                <span className="text-xs" style={{ color: 'var(--text-secondary)' }}>{timeAgo}</span>
                              </div>
                              <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>{notification.message}</p>
                              <div className="flex items-center mt-2">
                                {isUnread && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mr-2" style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                                    New
                                  </span>
                                )}
                                <span className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                                  {notification.type.charAt(0).toUpperCase() + notification.type.slice(1).replace('_', ' ')}
                                </span>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Bell className="w-12 h-12 mx-auto mb-4" style={{ color: 'var(--text-muted)' }} />
                      <h4 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>No Notifications Yet</h4>
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>You'll see important updates and reminders here.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Notification Statistics & Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Notification Statistics */}
                <div className="rounded-lg shadow-sm border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="p-6">
                    <h3 className="text-lg font-medium mb-4" style={{ color: 'var(--text-primary)' }}>Notification Summary</h3>
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center p-3 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)' }}>
                        <div className="text-2xl font-bold" style={{ color: 'var(--primary)' }}>
                          {notificationsLoading ? '...' : notificationStats.unread}
                        </div>
                        <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Unread</div>
                      </div>
                      <div className="text-center p-3 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)' }}>
                        <div className="text-2xl font-bold" style={{ color: 'var(--primary)' }}>
                          {notificationsLoading ? '...' : notificationStats.today}
                        </div>
                        <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Today's</div>
                      </div>
                      <div className="text-center p-3 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)' }}>
                        <div className="text-2xl font-bold" style={{ color: 'var(--primary)' }}>
                          {notificationsLoading ? '...' : notificationStats.thisWeek}
                        </div>
                        <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>This Week</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Notification Actions */}
                <div className="rounded-lg shadow-sm border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="p-6">
                    <h3 className="text-lg font-medium mb-4" style={{ color: 'var(--text-primary)' }}>Quick Actions</h3>
                    <div className="space-y-3">
                      <button className="w-full flex items-center p-3 border rounded-lg transition-colors"
                              style={{ borderColor: 'var(--border-medium)', backgroundColor: 'var(--bg-primary)', color: 'var(--text-primary)' }}
                              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}>
                        <CheckCircle className="w-5 h-5 mr-3" style={{ color: 'var(--primary)' }} />
                        <span className="text-sm font-medium">Mark All as Read</span>
                      </button>
                      <button className="w-full flex items-center p-3 border rounded-lg transition-colors"
                              style={{ borderColor: 'var(--border-medium)', backgroundColor: 'var(--bg-primary)', color: 'var(--text-primary)' }}
                              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}>
                        <Bell className="w-5 h-5 mr-3" style={{ color: 'var(--primary)' }} />
                        <span className="text-sm font-medium">Notification Settings</span>
                      </button>
                      <button className="w-full flex items-center p-3 border rounded-lg transition-colors"
                              style={{ borderColor: 'var(--border-medium)', backgroundColor: 'var(--bg-primary)', color: 'var(--text-primary)' }}
                              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}>
                        <Settings className="w-5 h-5 mr-3" style={{ color: 'var(--text-secondary)' }} />
                        <span className="text-sm font-medium">Manage Preferences</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-8">
              {/* Account Settings */}
              <div className="rounded-xl shadow-sm border"
                   style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="p-8">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>Account Settings</h3>
                    <button className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                            style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                      <Settings className="w-4 h-4 mr-2 inline" />
                      Edit Profile
                    </button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-6">
                      <div className="p-6 rounded-lg border"
                           style={{ borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-secondary)' }}>
                        <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Email Address</label>
                        <p className="text-base font-medium" style={{ color: 'var(--text-primary)' }}>{user?.email}</p>
                      </div>
                      <div className="p-6 rounded-lg border"
                           style={{ borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-secondary)' }}>
                        <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Full Name</label>
                        <p className="text-base font-medium" style={{ color: 'var(--text-primary)' }}>{user?.first_name} {user?.last_name}</p>
                      </div>
                    </div>
                    <div className="space-y-6">
                      <div className="p-6 rounded-lg border"
                           style={{ borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-secondary)' }}>
                        <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Account Role</label>
                        <p className="text-base font-medium capitalize" style={{ color: 'var(--text-primary)' }}>{user?.role || 'User'}</p>
                      </div>
                      <div className="p-6 rounded-lg border"
                           style={{ borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-secondary)' }}>
                        <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Member Since</label>
                        <p className="text-base font-medium" style={{ color: 'var(--text-primary)' }}>
                          {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long' })}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="mt-8 pt-6" style={{ borderTop: '1px solid var(--border-light)' }}>
                    <h4 className="text-lg font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>Quick Actions</h4>
                    <div className="flex flex-wrap gap-4">
                      <button className="px-4 py-2 rounded-lg border font-medium transition-colors"
                              style={{ borderColor: 'var(--border-medium)', color: 'var(--text-secondary)' }}>
                        Change Password
                      </button>
                      <button className="px-4 py-2 rounded-lg border font-medium transition-colors"
                              style={{ borderColor: 'var(--border-medium)', color: 'var(--text-secondary)' }}>
                        Privacy Settings
                      </button>
                      <button className="px-4 py-2 rounded-lg border font-medium transition-colors"
                              style={{ borderColor: 'var(--border-medium)', color: 'var(--text-secondary)' }}>
                        Download Data
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
