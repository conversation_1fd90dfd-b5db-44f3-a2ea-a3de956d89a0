import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { dataService } from '../lib/dataService'
import { Search, Filter } from 'lucide-react'

interface Caregiver {
  id: string
  name: string
  bio: string
  location: string
  specialties: string[]
  verified: boolean
  provider_type: string
  hourly_rate?: number
  years_experience?: number
  profile_image?: string
}

export default function Caregivers() {
  const navigate = useNavigate()
  const [caregivers, setCaregivers] = useState<Caregiver[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchName, setSearchName] = useState('')
  const [searchLocation, setSearchLocation] = useState('')
  const [availability, setAvailability] = useState('Any availability')
  const [maxRate, setMaxRate] = useState(200)
  const [minExperience, setMinExperience] = useState(0)
  // SURGICAL FIX: Add flag to prevent infinite loop
  const [isRequestInProgress, setIsRequestInProgress] = useState(false)

  useEffect(() => {
    // SURGICAL FIX: Prevent infinite loop with request flag
    if (isRequestInProgress) {
      console.log('Request already in progress, skipping...')
      return
    }

    async function fetchCaregivers() {
      try {
        setIsRequestInProgress(true)
        setLoading(true)
        setError(null)
        console.log('Fetching caregivers from database...')
        const data = await dataService.getCaregivers()
        console.log('Caregivers data received:', data)
        setCaregivers(data || [])
      } catch (err) {
        console.error('Error loading caregivers:', err)
        setError(err instanceof Error ? err.message : 'Failed to load caregivers')
        setCaregivers([])
      } finally {
        setLoading(false)
        setIsRequestInProgress(false)
      }
    }

    fetchCaregivers()
  }, []) // Only run on mount, not when isRequestInProgress changes

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--primary)' }}></div>
            <div>
              <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Loading Caregivers</div>
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Finding the best care providers for you...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-accent)' }}>
              <Search className="w-5 h-5" style={{ color: 'var(--primary)' }} />
            </div>
            <div>
              <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Unable to Load Caregivers</div>
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>{error}</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Filter caregivers based on search criteria
  const filteredCaregivers = caregivers.filter(caregiver => {
    const matchesName = !searchName || caregiver.name.toLowerCase().includes(searchName.toLowerCase())
    const matchesLocation = !searchLocation || caregiver.location.toLowerCase().includes(searchLocation.toLowerCase())
    const matchesRate = !caregiver.hourly_rate || caregiver.hourly_rate <= maxRate
    const matchesExperience = !caregiver.years_experience || caregiver.years_experience >= minExperience

    return matchesName && matchesLocation && matchesRate && matchesExperience
  })

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header */}
      <div
        className="px-4 sm:px-8 py-8"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderBottom: '1px solid var(--border-light)'
        }}
      >
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-4xl sm:text-5xl font-semibold mb-2" style={{ color: 'var(--text-primary)', fontWeight: '600', letterSpacing: '0.02em', lineHeight: '1.1' }}>Find Trusted Caregivers</h1>
              <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>
                {filteredCaregivers.length} verified professional{filteredCaregivers.length !== 1 ? 's' : ''} available
              </p>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)' }}>
              <Search className="w-5 h-5" style={{ color: 'var(--primary)' }} />
              <span className="font-medium" style={{ color: 'var(--primary)' }}>Active Search</span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <div
          className="w-full lg:w-80 p-4 lg:p-6"
          style={{
            backgroundColor: 'var(--bg-primary)',
            borderRight: '1px solid var(--border-light)',
            borderBottom: '1px solid var(--border-light)'
          }}
        >
          <div className="flex items-center gap-3 mb-8">
            <Filter className="w-5 h-5" style={{ color: 'var(--text-secondary)' }} />
            <h2 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>Search & Filters</h2>
          </div>

          {/* Search by name */}
          <div className="mb-7">
            <label className="block text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>
              Search by name
            </label>
            <input
              type="text"
              placeholder="Search caregiver by name..."
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              className="w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 shadow-sm"
              style={{
                border: '1px solid var(--border-light)',
                backgroundColor: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                fontSize: '14px'
              }}
            />
          </div>

          {/* Location */}
          <div className="mb-7">
            <label className="block text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>
              Location
            </label>
            <input
              type="text"
              placeholder="Enter city, state, or ZIP code..."
              value={searchLocation}
              onChange={(e) => setSearchLocation(e.target.value)}
              className="w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 shadow-sm"
              style={{
                border: '1px solid var(--border-light)',
                backgroundColor: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                fontSize: '14px'
              }}
            />
          </div>

          {/* Hourly Rate Filter */}
          <div className="mb-8">
            <label className="block text-sm font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
              Max Hourly Rate: ${maxRate}
            </label>
            <input
              type="range"
              min="20"
              max="200"
              value={maxRate}
              onChange={(e) => setMaxRate(Number(e.target.value))}
              className="w-full h-3 rounded-xl appearance-none cursor-pointer transition-all duration-300 hover:shadow-lg"
              style={{
                background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${((maxRate - 20) / (200 - 20)) * 100}%, var(--border-light) ${((maxRate - 20) / (200 - 20)) * 100}%, var(--border-light) 100%)`,
                outline: 'none',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), inset 0 1px 2px rgba(0, 0, 0, 0.05)'
              }}
            />
            <div className="flex justify-between text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
              <span>$20</span>
              <span>$200</span>
            </div>
          </div>

          {/* Experience Filter */}
          <div className="mb-8">
            <label className="block text-sm font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
              Min Experience: {minExperience} years
            </label>
            <input
              type="range"
              min="0"
              max="20"
              value={minExperience}
              onChange={(e) => setMinExperience(Number(e.target.value))}
              className="w-full h-3 rounded-xl appearance-none cursor-pointer transition-all duration-300 hover:shadow-lg"
              style={{
                background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(minExperience / 20) * 100}%, var(--border-light) ${(minExperience / 20) * 100}%, var(--border-light) 100%)`,
                outline: 'none',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), inset 0 1px 2px rgba(0, 0, 0, 0.05)'
              }}
            />
            <div className="flex justify-between text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
              <span>0 years</span>
              <span>20+ years</span>
            </div>
          </div>

          {/* Availability Filter */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
              Availability
            </label>
            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 shadow-sm"
              style={{
                border: '1px solid var(--border-light)',
                backgroundColor: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                fontSize: '14px'
              }}
            >
              <option value="Any availability">Any availability</option>
              <option value="Available now">Available now</option>
              <option value="Weekdays">Weekdays only</option>
              <option value="Weekends">Weekends only</option>
              <option value="24/7">24/7 availability</option>
            </select>
          </div>

          {/* Reset Filters */}
          <button
            className="w-full py-3 px-6 rounded-xl font-semibold transition-all duration-200 shadow-sm hover:shadow-md"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--bg-primary)'
            }}
            onClick={() => {
              setSearchName('')
              setSearchLocation('')
              setAvailability('Any availability')
              setMaxRate(200)
              setMinExperience(0)
            }}
          >
            🔍 Search Caregivers
          </button>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-4 sm:p-6">
          {filteredCaregivers.length === 0 ? (
            <div className="text-center py-16">
              <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-accent)' }}>
                  <Search className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                </div>
                <div>
                  <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>No Caregivers Found</div>
                  <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Try adjusting your search filters or check back later</div>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredCaregivers.map((caregiver) => (
                <div
                  key={caregiver.id}
                  className="rounded-2xl p-8 transition-all duration-300 transform cursor-pointer"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)'
                  }}
                  onClick={() => navigate(`/provider/caregivers/${caregiver.id}`)}
                >
                  {/* Profile Initials */}
                  <div className="text-center mb-4">
                    <div
                      className="w-16 h-16 rounded-full mx-auto mb-3 flex items-center justify-center"
                      style={{ backgroundColor: 'var(--bg-secondary)' }}
                    >
                      <span className="text-lg font-semibold" style={{ color: 'var(--text-secondary)' }}>
                        {caregiver.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </span>
                    </div>

                    {/* Name and Status */}
                    <div className="text-center">
                      <h3 className="font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>{caregiver.name}</h3>
                      {caregiver.verified && (
                        <span className="inline-flex items-center gap-1 text-xs" style={{ color: 'var(--text-secondary)' }}>
                          ✓ Verified
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Bio */}
                  <p className="text-sm text-center mb-4" style={{ color: 'var(--text-secondary)' }}>
                    {caregiver.bio}
                  </p>

                  {/* Location */}
                  <div className="text-center text-sm mb-4" style={{ color: 'var(--text-secondary)' }}>
                    📍 {caregiver.location}
                  </div>

                  {/* View Profile Button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      navigate(`/provider/caregivers/${caregiver.id}`)
                    }}
                    className="w-full py-2 px-4 rounded-lg font-medium transition-colors"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                  >
                    View Profile
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
