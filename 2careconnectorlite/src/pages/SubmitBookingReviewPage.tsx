import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Star, Send, ArrowLeft, CheckCircle2, AlertTriangle, MessageSquare, ThumbsUp, ThumbsDown } from 'lucide-react';
import { format, parseISO } from 'date-fns';

interface Booking {
  id: string;
  user_id: string;
  provider_id: string;
  provider_type: string;
  service_type: string;
  start_time: string;
  end_time: string;
  status: string;
  total_cost: number;
  special_requirements?: string;
  location?: string;
  provider_name?: string;
  created_at: string;
}

interface ReviewFormData {
  rating: number;
  review_text: string;
  would_recommend: boolean;
  service_quality: number;
  communication: number;
  professionalism: number;
  punctuality: number;
  value_for_money: number;
}

const SubmitBookingReviewPage: React.FC = () => {
  const navigate = useNavigate();
  const { bookingId } = useParams<{ bookingId: string }>();
  
  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [existingReview, setExistingReview] = useState<any>(null);
  
  const [formData, setFormData] = useState<ReviewFormData>({
    rating: 0,
    review_text: '',
    would_recommend: true,
    service_quality: 0,
    communication: 0,
    professionalism: 0,
    punctuality: 0,
    value_for_money: 0
  });

  useEffect(() => {
    const fetchBookingAndReview = async () => {
      if (!bookingId) {
        setError("Booking ID is missing.");
        setLoading(false);
        return;
      }

      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          setError("Please sign in to submit a review.");
          setLoading(false);
          return;
        }

        // Fetch booking details
        const { data: bookingData, error: bookingError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('id', bookingId)
          .eq('user_id', user.id) // Ensure user owns this booking
          .single();

        if (bookingError) {
          console.error('Booking fetch error:', bookingError);
          throw bookingError;
        }

        if (!bookingData) {
          throw new Error("Booking not found or you don't have permission to review it.");
        }

        // Check if booking is completed
        if (bookingData.status !== 'completed') {
          throw new Error("You can only review completed bookings.");
        }

        setBooking(bookingData);

        // Check if review already exists
        const { data: reviewData, error: reviewError } = await supabase
          .schema('care_connector')
          .from('service_provider_reviews')
          .select('*')
          .eq('booking_id', bookingId)
          .eq('user_id', user.id)
          .single();

        if (reviewError && reviewError.code !== 'PGRST116') { // PGRST116 = not found
          console.warn('Review fetch error (non-critical):', reviewError);
        } else if (reviewData) {
          setExistingReview(reviewData);
          // Pre-populate form with existing review
          setFormData({
            rating: reviewData.rating || 0,
            review_text: reviewData.review_text || '',
            would_recommend: reviewData.would_recommend ?? true,
            service_quality: reviewData.service_quality || 0,
            communication: reviewData.communication || 0,
            professionalism: reviewData.professionalism || 0,
            punctuality: reviewData.punctuality || 0,
            value_for_money: reviewData.value_for_money || 0
          });
        }

      } catch (err: any) {
        console.error("Error fetching booking and review:", err.message);
        setError(err.message || "Failed to load booking details.");
        setBooking(null);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingAndReview();
  }, [bookingId]);

  const handleRatingChange = (field: keyof ReviewFormData, value: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTextChange = (field: keyof ReviewFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleRecommendationChange = (value: boolean) => {
    setFormData(prev => ({
      ...prev,
      would_recommend: value
    }));
  };

  const validateForm = () => {
    if (formData.rating === 0) {
      return "Please provide an overall rating.";
    }
    
    if (formData.review_text.trim().length < 10) {
      return "Please write at least 10 characters in your review.";
    }
    
    if (formData.service_quality === 0 || formData.communication === 0 || 
        formData.professionalism === 0 || formData.punctuality === 0 || 
        formData.value_for_money === 0) {
      return "Please rate all aspects of the service.";
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }

    setSubmitting(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Please sign in to submit a review.');
      }

      const reviewData = {
        booking_id: bookingId,
        user_id: user.id,
        provider_id: booking!.provider_id,
        rating: formData.rating,
        review_text: formData.review_text.trim(),
        would_recommend: formData.would_recommend,
        service_quality: formData.service_quality,
        communication: formData.communication,
        professionalism: formData.professionalism,
        punctuality: formData.punctuality,
        value_for_money: formData.value_for_money,
        service_type: booking!.service_type,
        provider_type: booking!.provider_type
      };

      let result;
      if (existingReview) {
        // Update existing review
        result = await supabase
          .schema('care_connector')
          .from('service_provider_reviews')
          .update(reviewData)
          .eq('id', existingReview.id)
          .select()
          .single();
      } else {
        // Create new review
        result = await supabase
          .schema('care_connector')
          .from('service_provider_reviews')
          .insert([reviewData])
          .select()
          .single();
      }

      if (result.error) {
        throw result.error;
      }

      console.log('Review submitted successfully:', result.data);
      
      // Navigate back to booking detail page with success message
      navigate(`/booking/${bookingId}`, { 
        state: { message: existingReview ? 'Review updated successfully!' : 'Review submitted successfully!' }
      });

    } catch (err: any) {
      console.error('Error submitting review:', err);
      alert(`Failed to submit review: ${err.message}`);
    } finally {
      setSubmitting(false);
    }
  };

  const renderStarRating = (field: keyof ReviewFormData, value: number, label: string) => {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">{label}</label>
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => handleRatingChange(field, star)}
              className={`p-1 rounded transition-colors ${
                star <= value 
                  ? 'text-yellow-500 hover:text-yellow-600' 
                  : 'text-gray-300 hover:text-gray-400'
              }`}
            >
              <Star className={`h-6 w-6 ${star <= value ? 'fill-current' : ''}`} />
            </button>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading booking details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Cannot Submit Review</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => navigate(-1)}
              className="bg-var(--logo-green) text-white px-6 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Booking Not Found</h2>
            <p className="text-gray-600 mb-6">The booking you are trying to review could not be found.</p>
            <button
              onClick={() => navigate('/my-bookings')}
              className="bg-var(--logo-green) text-white px-6 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              View My Bookings
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center">
            <button
              onClick={() => navigate(`/booking/${booking.id}`)}
              className="flex items-center text-gray-600 hover:text-gray-900 mr-4 font-medium"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Booking
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {existingReview ? 'Edit Review' : 'Submit Review'}
              </h1>
              <p className="text-gray-600">Share your experience with {booking.provider_name || 'your care provider'}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="space-y-6">
              
              {/* Booking Information */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Booking Details</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Service</label>
                    <p className="text-gray-900 font-medium">{booking.service_type}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Provider</label>
                    <p className="text-gray-900 font-medium">{booking.provider_name || 'Care Provider'}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Date</label>
                    <p className="text-gray-900 font-medium">{format(parseISO(booking.start_time), 'MMMM d, yyyy')}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Time</label>
                    <p className="text-gray-900 font-medium">
                      {format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Total Cost</label>
                    <p className="text-gray-900 font-medium">${booking.total_cost}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                    <p className="text-gray-900 font-medium capitalize">{booking.status}</p>
                  </div>
                </div>
              </div>

              {/* Overall Rating */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Overall Rating</h2>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-700 font-medium">How would you rate this service overall?</span>
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          type="button"
                          onClick={() => handleRatingChange('rating', star)}
                          className={`p-1 rounded transition-colors ${
                            star <= formData.rating 
                              ? 'text-yellow-500 hover:text-yellow-600' 
                              : 'text-gray-300 hover:text-gray-400'
                          }`}
                        >
                          <Star className={`h-8 w-8 ${star <= formData.rating ? 'fill-current' : ''}`} />
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  {formData.rating > 0 && (
                    <p className="text-sm text-gray-600">
                      {formData.rating === 1 && "Poor - Very unsatisfied"}
                      {formData.rating === 2 && "Fair - Somewhat unsatisfied"}
                      {formData.rating === 3 && "Good - Satisfied"}
                      {formData.rating === 4 && "Very Good - Very satisfied"}
                      {formData.rating === 5 && "Excellent - Extremely satisfied"}
                    </p>
                  )}
                </div>
              </div>

              {/* Detailed Ratings */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Rate Different Aspects</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {renderStarRating('service_quality', formData.service_quality, 'Service Quality')}
                  {renderStarRating('communication', formData.communication, 'Communication')}
                  {renderStarRating('professionalism', formData.professionalism, 'Professionalism')}
                  {renderStarRating('punctuality', formData.punctuality, 'Punctuality')}
                  {renderStarRating('value_for_money', formData.value_for_money, 'Value for Money')}
                </div>
              </div>

              {/* Written Review */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Written Review</h2>
                
                <textarea
                  placeholder="Share your experience... What went well? What could be improved? Would you recommend this provider to others?"
                  value={formData.review_text}
                  onChange={(e) => handleTextChange('review_text', e.target.value)}
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent resize-none"
                  required
                />
                
                <div className="mt-2 flex justify-between text-sm text-gray-500">
                  <span>Minimum 10 characters</span>
                  <span>{formData.review_text.length} characters</span>
                </div>
              </div>

              {/* Recommendation */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Recommendation</h2>
                
                <div className="space-y-3">
                  <p className="text-gray-700">Would you recommend this provider to others?</p>
                  
                  <div className="flex space-x-4">
                    <button
                      type="button"
                      onClick={() => handleRecommendationChange(true)}
                      className={`flex items-center px-6 py-3 rounded-xl border-2 transition-colors ${
                        formData.would_recommend 
                          ? 'border-var(--logo-green) bg-var(--logo-green) text-white' 
                          : 'border-gray-300 text-gray-700 hover:border-gray-400'
                      }`}
                    >
                      <ThumbsUp className="mr-2 h-4 w-4" />
                      Yes, I recommend
                    </button>
                    
                    <button
                      type="button"
                      onClick={() => handleRecommendationChange(false)}
                      className={`flex items-center px-6 py-3 rounded-xl border-2 transition-colors ${
                        !formData.would_recommend 
                          ? 'border-red-500 bg-red-500 text-white' 
                          : 'border-gray-300 text-gray-700 hover:border-gray-400'
                      }`}
                    >
                      <ThumbsDown className="mr-2 h-4 w-4" />
                      No, I don't recommend
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>

          {/* Review Summary Sidebar */}
          <div className="space-y-6">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {existingReview ? 'Update Review' : 'Review Summary'}
              </h3>
              
              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Overall Rating:</span>
                  <div className="flex items-center space-x-1">
                    {formData.rating > 0 ? (
                      <>
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        <span className="font-medium text-gray-900">{formData.rating}/5</span>
                      </>
                    ) : (
                      <span className="text-gray-400">Not rated</span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Review Length:</span>
                  <span className="font-medium text-gray-900">{formData.review_text.length} characters</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Recommendation:</span>
                  <span className={`font-medium ${formData.would_recommend ? 'text-var(--logo-green)' : 'text-red-600'}`}>
                    {formData.would_recommend ? 'Yes' : 'No'}
                  </span>
                </div>
                
                <div className="border-t border-gray-200 pt-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Service Quality:</span>
                      <span className="font-medium">{formData.service_quality}/5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Communication:</span>
                      <span className="font-medium">{formData.communication}/5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Professionalism:</span>
                      <span className="font-medium">{formData.professionalism}/5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Punctuality:</span>
                      <span className="font-medium">{formData.punctuality}/5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Value:</span>
                      <span className="font-medium">{formData.value_for_money}/5</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <button
                type="submit"
                onClick={handleSubmit}
                disabled={submitting || formData.rating === 0 || formData.review_text.trim().length < 10}
                className="w-full flex items-center justify-center px-6 py-4 bg-var(--logo-green) text-white rounded-xl font-medium hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {existingReview ? 'Updating...' : 'Submitting...'}
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    {existingReview ? 'Update Review' : 'Submit Review'}
                  </>
                )}
              </button>
              
              <button
                type="button"
                onClick={() => navigate(`/booking/${booking.id}`)}
                className="w-full mt-3 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-xl">
                <p className="text-xs text-blue-800">
                  <CheckCircle2 className="inline h-3 w-3 mr-1" />
                  Your review helps other families find quality care providers and helps providers improve their services.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubmitBookingReviewPage;
