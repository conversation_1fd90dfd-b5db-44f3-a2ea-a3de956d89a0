import React, { useState, useEffect } from 'react'
import { Calendar, Clock, User, MapPin, DollarSign, Star, Filter, Search, CheckCircle, XCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface ProviderAvailability {
  id: string
  provider_id: string
  date: string
  start_time: string
  end_time: string
  is_available: boolean
  hourly_rate: number
  max_hours_per_booking: number
  min_hours_per_booking: number
  provider_info?: {
    full_name: string
    avatar_url?: string
    rating?: number
    specialties?: string[]
    address?: string
    verified: boolean
  }
}

interface AvailabilitySlot {
  date: string
  start_time: string
  end_time: string
  provider: ProviderAvailability['provider_info']
  hourly_rate: number
  provider_id: string
  availability_id: string
}

const BookingAvailabilityPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [availabilities, setAvailabilities] = useState<ProviderAvailability[]>([])
  const [availableSlots, setAvailableSlots] = useState<AvailabilitySlot[]>([])
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedLocation, setSelectedLocation] = useState('')
  const [maxRate, setMaxRate] = useState('')
  const [minHours, setMinHours] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('date')

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      await loadAvailabilities()
      setLoading(false)
    }
    getUser()
  }, [])

  useEffect(() => {
    processAvailableSlots()
  }, [availabilities, selectedDate, selectedLocation, maxRate, minHours, searchTerm, sortBy])

  const loadAvailabilities = async () => {
    try {
      const { data, error } = await supabase
        .from('provider_availability')
        .select(`
          id, provider_id, date, start_time, end_time, is_available, 
          hourly_rate, max_hours_per_booking, min_hours_per_booking,
          profiles!provider_availability_provider_id_fkey(
            full_name, avatar_url, rating, specialties, address, verified
          )
        `)
        .eq('is_available', true)
        .gte('date', new Date().toISOString().split('T')[0])
        .order('date', { ascending: true })

      if (error) throw error

      const transformed = (data || []).map(availability => ({
        id: availability.id,
        provider_id: availability.provider_id,
        date: availability.date,
        start_time: availability.start_time,
        end_time: availability.end_time,
        is_available: availability.is_available,
        hourly_rate: availability.hourly_rate,
        max_hours_per_booking: availability.max_hours_per_booking || 8,
        min_hours_per_booking: availability.min_hours_per_booking || 1,
        provider_info: availability.profiles ? {
          full_name: availability.profiles.full_name,
          avatar_url: availability.profiles.avatar_url,
          rating: availability.profiles.rating,
          specialties: availability.profiles.specialties || [],
          address: availability.profiles.address,
          verified: availability.profiles.verified || false
        } : undefined
      }))

      setAvailabilities(transformed)
    } catch (error) {
      console.error('Error loading availabilities:', error)
    }
  }

  const processAvailableSlots = () => {
    let slots = availabilities.map(availability => ({
      date: availability.date,
      start_time: availability.start_time,
      end_time: availability.end_time,
      provider: availability.provider_info,
      hourly_rate: availability.hourly_rate,
      provider_id: availability.provider_id,
      availability_id: availability.id
    }))

    // Apply filters
    if (selectedDate) {
      slots = slots.filter(slot => slot.date === selectedDate)
    }

    if (selectedLocation) {
      slots = slots.filter(slot => 
        slot.provider?.address?.toLowerCase().includes(selectedLocation.toLowerCase())
      )
    }

    if (maxRate) {
      slots = slots.filter(slot => slot.hourly_rate <= parseFloat(maxRate))
    }

    if (searchTerm) {
      const search = searchTerm.toLowerCase()
      slots = slots.filter(slot =>
        slot.provider?.full_name.toLowerCase().includes(search) ||
        slot.provider?.specialties?.some(s => s.toLowerCase().includes(search))
      )
    }

    // Apply sorting
    slots.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(a.date).getTime() - new Date(b.date).getTime()
        case 'rate':
          return a.hourly_rate - b.hourly_rate
        case 'rating':
          return (b.provider?.rating || 0) - (a.provider?.rating || 0)
        case 'name':
          return (a.provider?.full_name || '').localeCompare(b.provider?.full_name || '')
        default:
          return 0
      }
    })

    setAvailableSlots(slots)
  }

  const bookTimeSlot = async (slot: AvailabilitySlot) => {
    if (!user) {
      window.location.href = '/auth'
      return
    }

    const bookingUrl = `/create-booking?provider_id=${slot.provider_id}&date=${slot.date}&start_time=${slot.start_time}&end_time=${slot.end_time}&rate=${slot.hourly_rate}`
    window.location.href = bookingUrl
  }

  const getTimeSlotDuration = (startTime: string, endTime: string) => {
    const start = new Date(`2000-01-01T${startTime}`)
    const end = new Date(`2000-01-01T${endTime}`)
    const diffMs = end.getTime() - start.getTime()
    const diffHours = diffMs / (1000 * 60 * 60)
    return diffHours
  }

  const formatTimeRange = (startTime: string, endTime: string) => {
    const duration = getTimeSlotDuration(startTime, endTime)
    return `${startTime} - ${endTime} (${duration}h)`
  }

  const getNextWeekDates = () => {
    const dates = []
    for (let i = 0; i < 7; i++) {
      const date = new Date()
      date.setDate(date.getDate() + i)
      dates.push(date.toISOString().split('T')[0])
    }
    return dates
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading available time slots...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Available Time Slots</h1>
          <p className="text-gray-600">Find and book available care provider time slots</p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Date Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
              <select
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
              >
                <option value="">Any Date</option>
                {getNextWeekDates().map(date => (
                  <option key={date} value={date}>
                    {new Date(date).toLocaleDateString('en-US', { 
                      weekday: 'short', 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </option>
                ))}
              </select>
            </div>

            {/* Location Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
              <input
                type="text"
                placeholder="Enter city or area"
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
              />
            </div>

            {/* Max Rate Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Max Rate ($/hr)</label>
              <input
                type="number"
                placeholder="100"
                value={maxRate}
                onChange={(e) => setMaxRate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
              />
            </div>

            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Provider or specialty"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                />
              </div>
            </div>

            {/* Sort */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
              >
                <option value="date">Date</option>
                <option value="rate">Hourly Rate</option>
                <option value="rating">Rating</option>
                <option value="name">Provider Name</option>
              </select>
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mb-6">
          <p className="text-gray-600">
            {availableSlots.length} available time slots found
            {selectedDate && ` for ${new Date(selectedDate).toLocaleDateString()}`}
          </p>
        </div>

        {/* Available Slots */}
        <div className="space-y-4">
          {availableSlots.length > 0 ? (
            availableSlots.map((slot, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-4">
                      {slot.provider?.avatar_url ? (
                        <img 
                          src={slot.provider.avatar_url} 
                          alt={slot.provider.full_name}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                          <User className="w-8 h-8 text-gray-600" />
                        </div>
                      )}
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-xl font-semibold text-gray-900">
                            {slot.provider?.full_name || 'Unknown Provider'}
                          </h3>
                          {slot.provider?.verified && (
                            <CheckCircle className="w-5 h-5 text-logo-green" />
                          )}
                        </div>
                        {slot.provider?.rating && (
                          <div className="flex items-center gap-1 mt-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-gray-600">{slot.provider.rating}</span>
                          </div>
                        )}
                        {slot.provider?.specialties && slot.provider.specialties.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {slot.provider.specialties.slice(0, 3).map((specialty, idx) => (
                              <span key={idx} className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                                {specialty}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {new Date(slot.date).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            month: 'long', 
                            day: 'numeric' 
                          })}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {formatTimeRange(slot.start_time, slot.end_time)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          ${slot.hourly_rate}/hour
                        </span>
                      </div>
                      {slot.provider?.address && (
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-600 truncate">
                            {slot.provider.address}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-sm text-gray-700">
                        <span className="font-medium">Total cost: </span>
                        ${(slot.hourly_rate * getTimeSlotDuration(slot.start_time, slot.end_time)).toFixed(2)} 
                        <span className="text-gray-500 ml-1">
                          ({getTimeSlotDuration(slot.start_time, slot.end_time)} hours)
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="ml-6 flex flex-col gap-2">
                    <button
                      onClick={() => bookTimeSlot(slot)}
                      className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors font-medium"
                    >
                      Book Now
                    </button>
                    <button
                      onClick={() => window.location.href = `/provider/${slot.provider_id}`}
                      className="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                    >
                      View Profile
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="bg-white rounded-lg shadow-sm p-12 text-center">
              <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No available time slots</h3>
              <p className="text-gray-600 mb-6">
                {selectedDate || selectedLocation || maxRate || searchTerm ? 
                  'Try adjusting your search filters to find more availability.' :
                  'No providers have available time slots at the moment.'
                }
              </p>
              <div className="flex justify-center gap-3">
                <button
                  onClick={() => {
                    setSelectedDate('')
                    setSelectedLocation('')
                    setMaxRate('')
                    setSearchTerm('')
                  }}
                  className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Clear Filters
                </button>
                <button
                  onClick={() => window.location.href = '/caregivers'}
                  className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                >
                  Browse All Providers
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => window.location.href = '/my-bookings'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Calendar className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">My Bookings</div>
              <div className="text-sm text-gray-600">View your current and past bookings</div>
            </button>
            <button
              onClick={() => window.location.href = '/booking-preferences'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Filter className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Booking Preferences</div>
              <div className="text-sm text-gray-600">Set your default booking preferences</div>
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <User className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Dashboard</div>
              <div className="text-sm text-gray-600">Return to main dashboard</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingAvailabilityPage
