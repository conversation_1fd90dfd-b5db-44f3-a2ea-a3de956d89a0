import React from 'react'
import { Link } from 'react-router-dom'
import { Search, Calendar, Users, FileText, MessageSquare, Star } from 'lucide-react'

export default function HowItWorks() {
  const steps = [
    {
      number: 1,
      icon: Search,
      title: "Find Your Perfect Match",
      description: "Find qualified caregivers matched to your specific needs and location.",
      features: [
        "Browse verified profiles with ratings and reviews",
        "Filter by specialties, availability, and rates"
      ],
      action: "Browse Providers",
      link: "/caregivers"
    },
    {
      number: 2,
      icon: Calendar,
      title: "Book & Schedule Care",
      description: "Book appointments with real-time availability and instant confirmation.",
      features: [
        "Real-time availability checking",
        "Instant booking confirmation"
      ],
      action: "Book Appointment",
      link: "/schedule"
    },
    {
      number: 3,
      icon: Users,
      title: "Create Care Groups",
      description: "Collaborate with family through private care groups and shared planning.",
      features: [
        "Invite family members and caregivers",
        "Share care plans and medical information"
      ],
      action: "Create Group",
      link: "/care-groups/create"
    },
    {
      number: 4,
      icon: FileText,
      title: "Care Notes & Coordination",
      description: "Organize care notes and coordinate with your care team effectively.",
      features: [
        "Manual care note organization",
        "Share notes with care team"
      ],
      action: "Manage Notes",
      link: "/notes"
    },
    {
      number: 5,
      icon: MessageSquare,
      title: "Stay Connected",
      description: "Communicate securely with your care team through messaging and coordination.",
      features: [
        "HIPAA-compliant messaging",
        "Group announcements and updates"
      ],
      action: "Message Team",
      link: "/messaging"
    },
    {
      number: 6,
      icon: Star,
      title: "Review & Improve",
      description: "Rate experiences and help improve care quality for the community.",
      features: [
        "Rate and review care providers",
        "Share feedback for service improvement"
      ],
      action: "Leave Review",
      link: "/reviews"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl mb-6">
            How <span style={{color: 'var(--primary)'}}>Care Connector</span> Works
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600">
            Connect with caregivers, coordinate with family, and manage care seamlessly.
          </p>
        </div>

        {/* Steps - 6 steps in 2 rows of 3 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {steps.map((step) => {
            const Icon = step.icon
            return (
              <div key={step.number} className="bg-white rounded-lg p-6 shadow-lg relative">
                {/* Step Number */}
                <div className="absolute -top-3 -right-3 w-8 h-8 text-white rounded-full flex items-center justify-center text-sm font-bold" style={{backgroundColor: 'var(--primary)'}}>
                  {step.number}
                </div>
                
                {/* Icon */}
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{backgroundColor: 'var(--bg-accent)'}}>
                  <Icon className="w-8 h-8" style={{color: 'var(--primary)'}} />
                </div>
                
                {/* Content */}
                <h3 className="text-lg font-semibold text-gray-900 mb-3 text-center">
                  {step.title}
                </h3>
                <p className="text-gray-600 mb-4 text-center text-sm">
                  {step.description}
                </p>
                
                {/* Features */}
                <ul className="text-sm text-gray-600 mb-4 space-y-1">
                  {step.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-2" style={{color: 'var(--primary)'}}>✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
                
                {/* Action Button */}
                <Link 
                  to={step.link}
                  className="block w-full text-center bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded text-sm font-medium transition-colors"
                >
                  {step.action} →
                </Link>
              </div>
            )
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Take <span style={{color: 'var(--primary)'}}>Control</span>?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Transform how you coordinate care with confidence, clarity, and support.
          </p>
          <div className="space-x-4">
            <Link
              to="/get-started"
              className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white transition-colors" style={{backgroundColor: 'var(--primary)'}} onMouseEnter={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--primary-dark)'} onMouseLeave={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--primary)'}
            >
              Get Started Free
            </Link>
            <Link
              to="/caregivers"
              className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              Browse Caregivers
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
