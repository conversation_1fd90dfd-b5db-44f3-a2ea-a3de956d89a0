import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { CreditCard, DollarSign, Calendar, User, CheckCircle, XCircle, Clock, Download, Receipt, Lock } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface PaymentRecord {
  id: string
  booking_id: string
  amount: number
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  payment_method: string
  transaction_id: string
  created_at: string
  updated_at: string
  booking_info?: {
    provider_name: string
    service_date: string
    duration: number
  }
}

interface PaymentMethod {
  id: string
  type: 'credit_card' | 'debit_card' | 'bank_transfer' | 'digital_wallet'
  last_four: string
  brand: string
  is_default: boolean
  expires_at: string
}

const BookingPaymentPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [activeTab, setActiveTab] = useState('payments')
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [selectedPayment, setSelectedPayment] = useState<PaymentRecord | null>(null)

  const [paymentStats, setPaymentStats] = useState({
    total_paid: 0,
    pending_amount: 0,
    successful_payments: 0,
    failed_payments: 0
  })

  const [newPaymentMethod, setNewPaymentMethod] = useState({
    type: 'credit_card' as 'credit_card' | 'debit_card' | 'bank_transfer' | 'digital_wallet',
    card_number: '',
    expiry_date: '',
    cvv: '',
    name: '',
    is_default: false
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user) {
        await loadPayments()
        await loadPaymentMethods()
      }
      setLoading(false)
    }
    getUser()
  }, [])

  const loadPayments = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('payments')
        .select(`
          id, booking_id, amount, status, payment_method, transaction_id, created_at, updated_at,
          bookings!payments_booking_id_fkey(
            id, service_date, duration_hours,
            profiles!bookings_provider_id_fkey(full_name)
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error

      const transformedPayments = (data || []).map(payment => ({
        id: payment.id,
        booking_id: payment.booking_id,
        amount: payment.amount,
        status: payment.status,
        payment_method: payment.payment_method,
        transaction_id: payment.transaction_id,
        created_at: payment.created_at,
        updated_at: payment.updated_at,
        booking_info: payment.bookings ? {
          provider_name: payment.bookings.profiles?.full_name || 'Unknown Provider',
          service_date: payment.bookings.service_date,
          duration: payment.bookings.duration_hours || 0
        } : undefined
      }))

      setPayments(transformedPayments)

      // Calculate stats
      const totalPaid = transformedPayments
        .filter(p => p.status === 'completed')
        .reduce((sum, p) => sum + p.amount, 0)
      
      const pendingAmount = transformedPayments
        .filter(p => p.status === 'pending')
        .reduce((sum, p) => sum + p.amount, 0)

      const successfulPayments = transformedPayments.filter(p => p.status === 'completed').length
      const failedPayments = transformedPayments.filter(p => p.status === 'failed').length

      setPaymentStats({
        total_paid: totalPaid,
        pending_amount: pendingAmount,
        successful_payments: successfulPayments,
        failed_payments: failedPayments
      })
    } catch (error) {
      console.error('Error loading payments:', error)
    }
  }

  const loadPaymentMethods = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('user_payment_methods')
        .select('*')
        .eq('user_id', user.id)
        .order('is_default', { ascending: false })

      if (error) throw error

      setPaymentMethods(data || [])
    } catch (error) {
      console.error('Error loading payment methods:', error)
    }
  }

  const addPaymentMethod = async () => {
    if (!user || !newPaymentMethod.card_number) return

    setProcessing(true)
    try {
      const { error } = await supabase
        .from('user_payment_methods')
        .insert({
          user_id: user.id,
          type: newPaymentMethod.type,
          last_four: newPaymentMethod.card_number.slice(-4),
          brand: 'VISA', // In real app, this would be detected
          expires_at: newPaymentMethod.expiry_date,
          is_default: newPaymentMethod.is_default
        })

      if (error) throw error

      await loadPaymentMethods()
      setNewPaymentMethod({
        type: 'credit_card',
        card_number: '',
        expiry_date: '',
        cvv: '',
        name: '',
        is_default: false
      })
    } catch (error) {
      console.error('Error adding payment method:', error)
    } finally {
      setProcessing(false)
    }
  }

  const retryPayment = async (paymentId: string) => {
    setProcessing(true)
    try {
      const { error } = await supabase
        .from('payments')
        .update({ 
          status: 'pending',
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentId)

      if (error) throw error

      await loadPayments()
    } catch (error) {
      console.error('Error retrying payment:', error)
    } finally {
      setProcessing(false)
    }
  }

  const downloadReceipt = (payment: PaymentRecord) => {
    // Generate simple receipt
    const receiptContent = `
CARE CONNECTOR RECEIPT
===================

Transaction ID: ${payment.transaction_id}
Date: ${new Date(payment.created_at).toLocaleDateString()}
Provider: ${payment.booking_info?.provider_name || 'Unknown'}
Service Date: ${payment.booking_info?.service_date ? new Date(payment.booking_info.service_date).toLocaleDateString() : 'N/A'}
Amount: $${payment.amount}
Status: ${payment.status.toUpperCase()}
Payment Method: ${payment.payment_method.replace('_', ' ').toUpperCase()}

Thank you for using Care Connector!
    `.trim()

    const blob = new Blob([receiptContent], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `receipt-${payment.transaction_id}.txt`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading payments...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header with Navigation */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
                <p className="text-gray-600 mt-1">Manage your payments, methods, and view transaction history</p>
              </div>
              <Link
                to="/dashboard"
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
        
        {/* Error State */}
        <div className="error-state">
          <div className="error-state-content">
            <Lock className="error-state-icon" />
            <h2 className="error-state-title">Access Restricted</h2>
            <p className="error-state-message">Please sign in to manage your payments and view transaction history.</p>
            <div className="error-state-actions">
              <button
                onClick={() => window.location.href = '/auth'}
                className="button-primary"
              >
                Sign In
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="button-secondary"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'payments', label: 'Payment History', icon: Receipt },
    { id: 'methods', label: 'Payment Methods', icon: CreditCard },
    { id: 'analytics', label: 'Payment Analytics', icon: DollarSign }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Payment Management</h1>
          <p className="text-gray-600">Manage your payments, methods, and view transaction history</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${paymentStats.total_paid.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Total Paid</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${paymentStats.pending_amount.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <CheckCircle className="w-8 h-8 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{paymentStats.successful_payments}</div>
                <div className="text-sm text-gray-600">Successful</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <XCircle className="w-8 h-8 text-red-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{paymentStats.failed_payments}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-logo-green text-logo-green'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>

          <div className="p-6">
            {/* Payment History Tab */}
            {activeTab === 'payments' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Payment History</h3>
                
                {payments.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Provider</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Method</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {payments.map((payment) => (
                          <tr key={payment.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {new Date(payment.created_at).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {payment.booking_info?.provider_name || 'Unknown Provider'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                              ${payment.amount}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                                payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                payment.status === 'failed' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {payment.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 capitalize">
                              {payment.payment_method.replace('_', ' ')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center gap-2">
                                {payment.status === 'completed' && (
                                  <button
                                    onClick={() => downloadReceipt(payment)}
                                    className="text-logo-green hover:text-green-600 transition-colors"
                                  >
                                    <Download className="w-4 h-4" />
                                  </button>
                                )}
                                {payment.status === 'failed' && (
                                  <button
                                    onClick={() => retryPayment(payment.id)}
                                    disabled={processing}
                                    className="text-blue-600 hover:text-blue-800 transition-colors disabled:opacity-50"
                                  >
                                    Retry
                                  </button>
                                )}
                                <button
                                  onClick={() => setSelectedPayment(payment)}
                                  className="text-gray-600 hover:text-gray-800 transition-colors"
                                >
                                  View
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Receipt className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No payments found</h3>
                    <p className="text-gray-600">Your payment history will appear here once you make bookings.</p>
                  </div>
                )}
              </div>
            )}

            {/* Payment Methods Tab */}
            {activeTab === 'methods' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Payment Methods</h3>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Existing Payment Methods */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">Saved Methods</h4>
                    {paymentMethods.length > 0 ? (
                      <div className="space-y-3">
                        {paymentMethods.map((method) => (
                          <div key={method.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div className="flex items-center gap-3">
                                <CreditCard className="w-6 h-6 text-gray-600" />
                                <div>
                                  <div className="font-medium text-gray-900">
                                    {method.brand} •••• {method.last_four}
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    Expires {method.expires_at}
                                  </div>
                                  {method.is_default && (
                                    <div className="text-xs text-logo-green font-medium">Default</div>
                                  )}
                                </div>
                              </div>
                              <button className="text-gray-400 hover:text-red-600 transition-colors">
                                Remove
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <CreditCard className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                        <p>No payment methods saved</p>
                      </div>
                    )}
                  </div>

                  {/* Add New Payment Method */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">Add New Method</h4>
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Card Number</label>
                          <input
                            type="text"
                            placeholder="1234 5678 9012 3456"
                            value={newPaymentMethod.card_number}
                            onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, card_number: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Expiry</label>
                            <input
                              type="text"
                              placeholder="MM/YY"
                              value={newPaymentMethod.expiry_date}
                              onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, expiry_date: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">CVV</label>
                            <input
                              type="text"
                              placeholder="123"
                              value={newPaymentMethod.cvv}
                              onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, cvv: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Cardholder Name</label>
                          <input
                            type="text"
                            placeholder="John Doe"
                            value={newPaymentMethod.name}
                            onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, name: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                          />
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="default"
                            checked={newPaymentMethod.is_default}
                            onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, is_default: e.target.checked }))}
                            className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded mr-2"
                          />
                          <label htmlFor="default" className="text-sm text-gray-700">Set as default payment method</label>
                        </div>

                        <button
                          onClick={addPaymentMethod}
                          disabled={processing || !newPaymentMethod.card_number}
                          className="w-full bg-logo-green text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
                        >
                          <Lock className="w-4 h-4" />
                          {processing ? 'Adding...' : 'Add Payment Method'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Analytics Tab */}
            {activeTab === 'analytics' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Payment Analytics</h3>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Payment Success Rate</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Success Rate</span>
                        <span className="font-semibold">
                          {payments.length > 0 ? 
                            ((paymentStats.successful_payments / payments.length) * 100).toFixed(1) : 0}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ 
                            width: `${payments.length > 0 ? 
                              (paymentStats.successful_payments / payments.length) * 100 : 0}%` 
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Monthly Spending</h4>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-logo-green mb-2">
                        ${paymentStats.total_paid.toFixed(2)}
                      </div>
                      <div className="text-sm text-gray-600">Total this period</div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 bg-gray-50 p-6 rounded-lg">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Payment Insights</h4>
                  <ul className="space-y-2 text-sm text-gray-700">
                    <li>• Average payment amount: ${payments.length > 0 ? (paymentStats.total_paid / paymentStats.successful_payments || 0).toFixed(2) : '0.00'}</li>
                    <li>• Most common payment method: Credit Card</li>
                    <li>• Peak payment day: Weekdays</li>
                    <li>• Payment completion time: Usually within 24 hours</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => window.location.href = '/my-bookings'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Calendar className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">My Bookings</div>
              <div className="text-sm text-gray-600">View appointments requiring payment</div>
            </button>
            <button
              onClick={() => window.location.href = '/booking-preferences'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <User className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Payment Preferences</div>
              <div className="text-sm text-gray-600">Manage payment settings</div>
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <div className="w-6 h-6 bg-logo-green rounded mb-2"></div>
              <div className="font-medium text-gray-900">Dashboard</div>
              <div className="text-sm text-gray-600">Return to main dashboard</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingPaymentPage
