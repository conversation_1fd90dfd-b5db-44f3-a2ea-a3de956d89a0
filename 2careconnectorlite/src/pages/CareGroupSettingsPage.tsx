import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Settings, Users, Shield, Bell, Trash2, Save, ArrowLeft, AlertCircle, CheckCircle, Edit3, Plus, X } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface CareGroupSettings {
  id: string
  name: string
  description: string
  group_type: 'public' | 'private' | 'support'
  location: string
  max_members: number
  tags: string[]
  rules: string[]
  meeting_schedule?: {
    frequency: string
    day_of_week: string
    time: string
    location: string
  }
  notification_settings: {
    new_members: boolean
    new_messages: boolean
    events: boolean
    weekly_digest: boolean
  }
}

interface GroupMember {
  user_id: string
  full_name: string
  avatar_url?: string
  role: 'admin' | 'moderator' | 'member'
  joined_at: string
  email?: string
}

const CareGroupSettingsPage: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>()
  const navigate = useNavigate()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('general')
  const [careGroup, setCareGroup] = useState<CareGroupSettings | null>(null)
  const [members, setMembers] = useState<GroupMember[]>([])
  const [userRole, setUserRole] = useState<string>('')
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [newRule, setNewRule] = useState('')
  const [newTag, setNewTag] = useState('')

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user && groupId) {
        await loadGroupSettings()
        await loadMembers()
        await checkUserRole()
      }
      setLoading(false)
    }
    getUser()
  }, [groupId])

  const loadGroupSettings = async () => {
    if (!groupId) return

    try {
      const { data, error } = await supabase
        .from('care_groups')
        .select('*')
        .eq('id', groupId)
        .single()

      if (error) throw error

      setCareGroup({
        id: data.id,
        name: data.name,
        description: data.description,
        group_type: data.group_type,
        location: data.location,
        max_members: data.max_members,
        tags: data.tags || [],
        rules: data.rules || [],
        meeting_schedule: data.meeting_schedule,
        notification_settings: data.notification_settings || {
          new_members: true,
          new_messages: true,
          events: true,
          weekly_digest: false
        }
      })
    } catch (error) {
      console.error('Error loading group settings:', error)
    }
  }

  const loadMembers = async () => {
    if (!groupId) return

    try {
      const { data, error } = await supabase
        .from('care_group_members')
        .select(`
          user_id, role, joined_at,
          profiles!care_group_members_user_id_fkey(full_name, avatar_url, email)
        `)
        .eq('group_id', groupId)
        .order('joined_at', { ascending: true })

      if (error) throw error

      setMembers((data || []).map(member => ({
        user_id: member.user_id,
        full_name: member.profiles?.full_name || 'Unknown User',
        avatar_url: member.profiles?.avatar_url,
        role: member.role,
        joined_at: member.joined_at,
        email: member.profiles?.email
      })))
    } catch (error) {
      console.error('Error loading members:', error)
    }
  }

  const checkUserRole = async () => {
    if (!user || !groupId) return

    try {
      const { data, error } = await supabase
        .from('care_group_members')
        .select('role')
        .eq('group_id', groupId)
        .eq('user_id', user.id)
        .single()

      if (error) throw error
      setUserRole(data.role)
    } catch (error) {
      console.error('Error checking user role:', error)
    }
  }

  const saveGeneralSettings = async () => {
    if (!careGroup || !groupId) return

    setSaving(true)
    try {
      const { error } = await supabase
        .from('care_groups')
        .update({
          name: careGroup.name,
          description: careGroup.description,
          group_type: careGroup.group_type,
          location: careGroup.location,
          max_members: careGroup.max_members,
          tags: careGroup.tags,
          rules: careGroup.rules,
          meeting_schedule: careGroup.meeting_schedule
        })
        .eq('id', groupId)

      if (error) throw error
      alert('Settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      alert('Error saving settings. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const saveNotificationSettings = async () => {
    if (!careGroup || !groupId) return

    setSaving(true)
    try {
      const { error } = await supabase
        .from('care_groups')
        .update({
          notification_settings: careGroup.notification_settings
        })
        .eq('id', groupId)

      if (error) throw error
      alert('Notification settings saved!')
    } catch (error) {
      console.error('Error saving notification settings:', error)
      alert('Error saving notification settings. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const updateMemberRole = async (memberId: string, newRole: 'admin' | 'moderator' | 'member') => {
    try {
      const { error } = await supabase
        .from('care_group_members')
        .update({ role: newRole })
        .eq('group_id', groupId)
        .eq('user_id', memberId)

      if (error) throw error
      await loadMembers()
      alert('Member role updated successfully!')
    } catch (error) {
      console.error('Error updating member role:', error)
      alert('Error updating member role. Please try again.')
    }
  }

  const removeMember = async (memberId: string) => {
    if (!confirm('Are you sure you want to remove this member?')) return

    try {
      const { error } = await supabase
        .from('care_group_members')
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', memberId)

      if (error) throw error
      await loadMembers()
      alert('Member removed successfully!')
    } catch (error) {
      console.error('Error removing member:', error)
      alert('Error removing member. Please try again.')
    }
  }

  const deleteGroup = async () => {
    if (!groupId) return

    try {
      // Delete all members first
      await supabase.from('care_group_members').delete().eq('group_id', groupId)
      
      // Delete all activity
      await supabase.from('care_group_activity').delete().eq('group_id', groupId)
      
      // Delete the group
      const { error } = await supabase.from('care_groups').delete().eq('id', groupId)
      
      if (error) throw error
      
      alert('Care group deleted successfully!')
      navigate('/care-groups')
    } catch (error) {
      console.error('Error deleting group:', error)
      alert('Error deleting group. Please try again.')
    }
  }

  const addRule = () => {
    if (!newRule.trim() || !careGroup) return
    
    setCareGroup({
      ...careGroup,
      rules: [...careGroup.rules, newRule.trim()]
    })
    setNewRule('')
  }

  const removeRule = (index: number) => {
    if (!careGroup) return
    
    setCareGroup({
      ...careGroup,
      rules: careGroup.rules.filter((_, i) => i !== index)
    })
  }

  const addTag = () => {
    if (!newTag.trim() || !careGroup) return
    
    setCareGroup({
      ...careGroup,
      tags: [...careGroup.tags, newTag.trim()]
    })
    setNewTag('')
  }

  const removeTag = (index: number) => {
    if (!careGroup) return
    
    setCareGroup({
      ...careGroup,
      tags: careGroup.tags.filter((_, i) => i !== index)
    })
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'moderator': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    )
  }

  if (!careGroup || userRole !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin permissions to access group settings.</p>
          <button
            onClick={() => navigate(`/care-groups/${groupId}`)}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Back to Group
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate(`/care-groups/${groupId}`)}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Group
          </button>
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-logo-green rounded-full flex items-center justify-center">
              <Settings className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Group Settings</h1>
              <p className="text-gray-600">{careGroup.name}</p>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'general', name: 'General', icon: Settings },
                { id: 'members', name: 'Members', icon: Users },
                { id: 'notifications', name: 'Notifications', icon: Bell },
                { id: 'danger', name: 'Danger Zone', icon: Shield }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-logo-green text-logo-green'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {/* General Settings */}
          {activeTab === 'general' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">General Settings</h3>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Group Name</label>
                    <input
                      type="text"
                      value={careGroup.name}
                      onChange={(e) => setCareGroup({ ...careGroup, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Group Type</label>
                    <select
                      value={careGroup.group_type}
                      onChange={(e) => setCareGroup({ ...careGroup, group_type: e.target.value as any })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    >
                      <option value="public">Public</option>
                      <option value="private">Private</option>
                      <option value="support">Support</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    value={careGroup.description}
                    onChange={(e) => setCareGroup({ ...careGroup, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={careGroup.location}
                      onChange={(e) => setCareGroup({ ...careGroup, location: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Max Members</label>
                    <input
                      type="number"
                      value={careGroup.max_members}
                      onChange={(e) => setCareGroup({ ...careGroup, max_members: parseInt(e.target.value) })}
                      min="1"
                      max="500"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    />
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {careGroup.tags.map((tag, index) => (
                      <span key={index} className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full flex items-center gap-2">
                        {tag}
                        <button onClick={() => removeTag(index)} className="text-gray-500 hover:text-red-500">
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add new tag"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                    />
                    <button
                      onClick={addTag}
                      className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Rules */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Group Rules</label>
                  <div className="space-y-2 mb-3">
                    {careGroup.rules.map((rule, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <span className="bg-logo-green text-white text-xs w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0">
                          {index + 1}
                        </span>
                        <span className="flex-1 text-gray-700">{rule}</span>
                        <button onClick={() => removeRule(index)} className="text-gray-500 hover:text-red-500">
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={newRule}
                      onChange={(e) => setNewRule(e.target.value)}
                      placeholder="Add new rule"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                      onKeyPress={(e) => e.key === 'Enter' && addRule()}
                    />
                    <button
                      onClick={addRule}
                      className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <button
                  onClick={saveGeneralSettings}
                  disabled={saving}
                  className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2 disabled:opacity-50"
                >
                  <Save className="w-4 h-4" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </div>
          )}

          {/* Members Management */}
          {activeTab === 'members' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Manage Members ({members.length})
              </h3>
              
              <div className="space-y-4">
                {members.map((member) => (
                  <div key={member.user_id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center gap-4">
                      {member.avatar_url ? (
                        <img 
                          src={member.avatar_url} 
                          alt={member.full_name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                          <Users className="w-6 h-6 text-gray-600" />
                        </div>
                      )}
                      <div>
                        <div className="font-medium text-gray-900">{member.full_name}</div>
                        <div className="text-sm text-gray-600">
                          Joined {new Date(member.joined_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <select
                        value={member.role}
                        onChange={(e) => updateMemberRole(member.user_id, e.target.value as any)}
                        disabled={member.user_id === user?.id}
                        className={`px-3 py-1 text-xs font-semibold rounded-full border-0 ${getRoleColor(member.role)}`}
                      >
                        <option value="member">Member</option>
                        <option value="moderator">Moderator</option>
                        <option value="admin">Admin</option>
                      </select>
                      
                      {member.user_id !== user?.id && (
                        <button
                          onClick={() => removeMember(member.user_id)}
                          className="text-red-500 hover:text-red-700 p-1"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Notifications */}
          {activeTab === 'notifications' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Notification Settings</h3>
              
              <div className="space-y-6">
                {[
                  { key: 'new_members', label: 'New Members', description: 'Get notified when someone joins the group' },
                  { key: 'new_messages', label: 'New Messages', description: 'Get notified about new group messages' },
                  { key: 'events', label: 'Events', description: 'Get notified about upcoming events' },
                  { key: 'weekly_digest', label: 'Weekly Digest', description: 'Receive a weekly summary of group activity' }
                ].map((setting) => (
                  <div key={setting.key} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{setting.label}</div>
                      <div className="text-sm text-gray-600">{setting.description}</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={careGroup.notification_settings[setting.key as keyof typeof careGroup.notification_settings]}
                        onChange={(e) => setCareGroup({
                          ...careGroup,
                          notification_settings: {
                            ...careGroup.notification_settings,
                            [setting.key]: e.target.checked
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-logo-green"></div>
                    </label>
                  </div>
                ))}
                
                <button
                  onClick={saveNotificationSettings}
                  disabled={saving}
                  className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2 disabled:opacity-50"
                >
                  <Save className="w-4 h-4" />
                  {saving ? 'Saving...' : 'Save Notification Settings'}
                </button>
              </div>
            </div>
          )}

          {/* Danger Zone */}
          {activeTab === 'danger' && (
            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-red-500">
              <h3 className="text-lg font-semibold text-red-900 mb-6">Danger Zone</h3>
              
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <div className="flex items-start gap-4">
                  <AlertCircle className="w-6 h-6 text-red-500 flex-shrink-0 mt-0.5" />
                  <div className="flex-1">
                    <h4 className="font-medium text-red-900 mb-2">Delete Care Group</h4>
                    <p className="text-red-700 mb-4">
                      This action cannot be undone. This will permanently delete the care group,
                      remove all members, and delete all associated data.
                    </p>
                    <button
                      onClick={() => setShowDeleteModal(true)}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete Group
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <div className="flex items-center gap-4 mb-4">
                <AlertCircle className="w-8 h-8 text-red-500" />
                <h3 className="text-lg font-semibold text-gray-900">Confirm Deletion</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Are you absolutely sure you want to delete this care group? This action cannot be undone.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="flex-1 bg-gray-100 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    deleteGroup()
                    setShowDeleteModal(false)
                  }}
                  className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                  Delete Forever
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CareGroupSettingsPage
