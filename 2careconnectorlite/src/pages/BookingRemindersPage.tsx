import React, { useState, useEffect } from 'react'
import { Bell, Clock, Calendar, Mail, Phone, MessageSquare, Settings, Plus, Edit, Trash2, Check, X } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface BookingReminder {
  id: string
  booking_id: string
  reminder_type: 'email' | 'sms' | 'push'
  reminder_time: number // hours before appointment
  message: string
  enabled: boolean
  created_at: string
  booking_info?: {
    provider_name: string
    service_date: string
    duration: number
  }
}

const BookingRemindersPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [reminders, setReminders] = useState<BookingReminder[]>([])
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingReminder, setEditingReminder] = useState<BookingReminder | null>(null)

  const [defaultSettings, setDefaultSettings] = useState({
    email_enabled: true,
    sms_enabled: true,
    push_enabled: true,
    default_timing: 24,
    auto_create: true
  })

  const [newReminder, setNewReminder] = useState({
    booking_id: '',
    reminder_type: 'email' as 'email' | 'sms' | 'push',
    reminder_time: 24,
    message: '',
    enabled: true
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user) {
        await loadReminders()
        await loadDefaultSettings()
      }
      setLoading(false)
    }
    getUser()
  }, [])

  const loadReminders = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('booking_reminders')
        .select(`
          id, booking_id, reminder_type, reminder_time, message, enabled, created_at,
          bookings!booking_reminders_booking_id_fkey(
            id, service_date, duration_hours,
            profiles!bookings_provider_id_fkey(full_name)
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error

      const transformedReminders = (data || []).map(reminder => ({
        id: reminder.id,
        booking_id: reminder.booking_id,
        reminder_type: reminder.reminder_type,
        reminder_time: reminder.reminder_time,
        message: reminder.message,
        enabled: reminder.enabled,
        created_at: reminder.created_at,
        booking_info: reminder.bookings ? {
          provider_name: reminder.bookings.profiles?.full_name || 'Unknown Provider',
          service_date: reminder.bookings.service_date,
          duration: reminder.bookings.duration_hours || 0
        } : undefined
      }))

      setReminders(transformedReminders)
    } catch (error) {
      console.error('Error loading reminders:', error)
    }
  }

  const loadDefaultSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('reminder_settings')
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading settings:', error)
        return
      }

      if (data?.reminder_settings) {
        setDefaultSettings(prev => ({ ...prev, ...data.reminder_settings }))
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const saveDefaultSettings = async () => {
    if (!user) return

    setSaving(true)
    try {
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          reminder_settings: defaultSettings,
          updated_at: new Date().toISOString()
        })

      if (error) throw error
    } catch (error) {
      console.error('Error saving settings:', error)
    } finally {
      setSaving(false)
    }
  }

  const createReminder = async () => {
    if (!user || !newReminder.booking_id) return

    setSaving(true)
    try {
      const { error } = await supabase
        .from('booking_reminders')
        .insert({
          user_id: user.id,
          ...newReminder
        })

      if (error) throw error

      await loadReminders()
      setShowAddModal(false)
      setNewReminder({
        booking_id: '',
        reminder_type: 'email',
        reminder_time: 24,
        message: '',
        enabled: true
      })
    } catch (error) {
      console.error('Error creating reminder:', error)
    } finally {
      setSaving(false)
    }
  }

  const updateReminder = async (id: string, updates: Partial<BookingReminder>) => {
    setSaving(true)
    try {
      const { error } = await supabase
        .from('booking_reminders')
        .update(updates)
        .eq('id', id)

      if (error) throw error

      await loadReminders()
      setEditingReminder(null)
    } catch (error) {
      console.error('Error updating reminder:', error)
    } finally {
      setSaving(false)
    }
  }

  const deleteReminder = async (id: string) => {
    if (!confirm('Are you sure you want to delete this reminder?')) return

    setSaving(true)
    try {
      const { error } = await supabase
        .from('booking_reminders')
        .delete()
        .eq('id', id)

      if (error) throw error

      await loadReminders()
    } catch (error) {
      console.error('Error deleting reminder:', error)
    } finally {
      setSaving(false)
    }
  }

  const toggleReminder = async (id: string, enabled: boolean) => {
    await updateReminder(id, { enabled })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading reminders...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <Bell className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Reminders Unavailable</h1>
          <p className="text-gray-600 mb-6">Please sign in to manage your booking reminders.</p>
          <button
            onClick={() => window.location.href = '/auth'}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Booking Reminders</h1>
              <p className="text-gray-600">Manage your appointment reminders and notification preferences</p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Reminder
            </button>
          </div>
        </div>

        {/* Default Settings */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Default Reminder Settings</h3>
            <button
              onClick={saveDefaultSettings}
              disabled={saving}
              className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-900">Email Reminders</label>
                  <p className="text-sm text-gray-600">Send email notifications</p>
                </div>
                <input
                  type="checkbox"
                  checked={defaultSettings.email_enabled}
                  onChange={(e) => setDefaultSettings(prev => ({ ...prev, email_enabled: e.target.checked }))}
                  className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-900">SMS Reminders</label>
                  <p className="text-sm text-gray-600">Send text message notifications</p>
                </div>
                <input
                  type="checkbox"
                  checked={defaultSettings.sms_enabled}
                  onChange={(e) => setDefaultSettings(prev => ({ ...prev, sms_enabled: e.target.checked }))}
                  className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-900">Push Notifications</label>
                  <p className="text-sm text-gray-600">Send app notifications</p>
                </div>
                <input
                  type="checkbox"
                  checked={defaultSettings.push_enabled}
                  onChange={(e) => setDefaultSettings(prev => ({ ...prev, push_enabled: e.target.checked }))}
                  className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">Default Timing</label>
                <select
                  value={defaultSettings.default_timing}
                  onChange={(e) => setDefaultSettings(prev => ({ ...prev, default_timing: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                >
                  <option value={1}>1 hour before</option>
                  <option value={2}>2 hours before</option>
                  <option value={4}>4 hours before</option>
                  <option value={12}>12 hours before</option>
                  <option value={24}>24 hours before</option>
                  <option value={48}>48 hours before</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-900">Auto-Create Reminders</label>
                  <p className="text-sm text-gray-600">Automatically create reminders for new bookings</p>
                </div>
                <input
                  type="checkbox"
                  checked={defaultSettings.auto_create}
                  onChange={(e) => setDefaultSettings(prev => ({ ...prev, auto_create: e.target.checked }))}
                  className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Active Reminders */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Active Reminders</h3>
          </div>

          <div className="p-6">
            {reminders.length > 0 ? (
              <div className="space-y-4">
                {reminders.map((reminder) => (
                  <div key={reminder.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          {reminder.reminder_type === 'email' && <Mail className="w-5 h-5 text-blue-600" />}
                          {reminder.reminder_type === 'sms' && <Phone className="w-5 h-5 text-green-600" />}
                          {reminder.reminder_type === 'push' && <Bell className="w-5 h-5 text-purple-600" />}
                          
                          <div>
                            <div className="font-medium text-gray-900">
                              {reminder.booking_info?.provider_name || 'Unknown Provider'}
                            </div>
                            <div className="text-sm text-gray-600">
                              {reminder.booking_info?.service_date ? 
                                new Date(reminder.booking_info.service_date).toLocaleDateString() : 
                                'Date not available'
                              }
                            </div>
                          </div>
                        </div>

                        <div className="text-sm text-gray-600 mb-2">
                          <Clock className="w-4 h-4 inline mr-1" />
                          {reminder.reminder_time} hours before appointment
                        </div>

                        {reminder.message && (
                          <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                            {reminder.message}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <input
                          type="checkbox"
                          checked={reminder.enabled}
                          onChange={(e) => toggleReminder(reminder.id, e.target.checked)}
                          className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                        />
                        <button
                          onClick={() => setEditingReminder(reminder)}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => deleteReminder(reminder.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Bell className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No reminders set</h3>
                <p className="text-gray-600 mb-6">Create your first reminder to stay notified about your appointments.</p>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                >
                  Add First Reminder
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => window.location.href = '/my-bookings'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Calendar className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">My Bookings</div>
              <div className="text-sm text-gray-600">View upcoming appointments</div>
            </button>
            <button
              onClick={() => window.location.href = '/booking-notifications'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <MessageSquare className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Notifications</div>
              <div className="text-sm text-gray-600">Manage notification history</div>
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <div className="w-6 h-6 bg-logo-green rounded mb-2"></div>
              <div className="font-medium text-gray-900">Dashboard</div>
              <div className="text-sm text-gray-600">Return to main dashboard</div>
            </button>
          </div>
        </div>

        {/* Add Reminder Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Add New Reminder</h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Reminder Type</label>
                  <select
                    value={newReminder.reminder_type}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, reminder_type: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value="email">Email</option>
                    <option value="sms">SMS</option>
                    <option value="push">Push Notification</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Timing</label>
                  <select
                    value={newReminder.reminder_time}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, reminder_time: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value={1}>1 hour before</option>
                    <option value={2}>2 hours before</option>
                    <option value={4}>4 hours before</option>
                    <option value={12}>12 hours before</option>
                    <option value={24}>24 hours before</option>
                    <option value={48}>48 hours before</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Custom Message (Optional)</label>
                  <textarea
                    value={newReminder.message}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Add a custom message for this reminder..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    rows={3}
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enabled"
                    checked={newReminder.enabled}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, enabled: e.target.checked }))}
                    className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded mr-2"
                  />
                  <label htmlFor="enabled" className="text-sm text-gray-700">Enable this reminder</label>
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={createReminder}
                  disabled={saving}
                  className="px-4 py-2 bg-logo-green text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
                >
                  {saving ? 'Creating...' : 'Create Reminder'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default BookingRemindersPage
