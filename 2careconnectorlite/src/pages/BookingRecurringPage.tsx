import React, { useState, useEffect } from 'react'
import { Repeat, Calendar, Clock, User, DollarSign, Settings, Pause, Play, Trash2, Edit } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface RecurringBooking {
  id: string
  provider_id: string
  frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly'
  start_date: string
  end_date?: string
  start_time: string
  end_time: string
  duration_hours: number
  hourly_rate: number
  is_active: boolean
  next_booking_date: string
  total_bookings_created: number
  special_instructions?: string
  days_of_week?: number[] // 0=Sunday, 1=Monday, etc.
  created_at: string
  provider_info?: {
    full_name: string
    avatar_url?: string
    rating?: number
    specialties?: string[]
  }
}

const BookingRecurringPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [recurringBookings, setRecurringBookings] = useState<RecurringBooking[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingBooking, setEditingBooking] = useState<RecurringBooking | null>(null)
  const [filterStatus, setFilterStatus] = useState('all')

  const [newRecurring, setNewRecurring] = useState({
    provider_id: '',
    frequency: 'weekly' as const,
    start_date: '',
    end_date: '',
    start_time: '',
    end_time: '',
    hourly_rate: '',
    days_of_week: [] as number[],
    special_instructions: ''
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user) {
        await loadRecurringBookings()
      }
      setLoading(false)
    }
    getUser()
  }, [filterStatus])

  const loadRecurringBookings = async () => {
    if (!user) return

    try {
      let query = supabase
        .from('recurring_bookings')
        .select(`
          id, provider_id, frequency, start_date, end_date, start_time, end_time,
          duration_hours, hourly_rate, is_active, next_booking_date, 
          total_bookings_created, special_instructions, days_of_week, created_at,
          profiles!recurring_bookings_provider_id_fkey(
            full_name, avatar_url, rating, specialties
          )
        `)
        .eq('client_id', user.id)
        .order('created_at', { ascending: false })

      if (filterStatus === 'active') {
        query = query.eq('is_active', true)
      } else if (filterStatus === 'paused') {
        query = query.eq('is_active', false)
      }

      const { data, error } = await query

      if (error) throw error

      const transformed = (data || []).map(booking => ({
        id: booking.id,
        provider_id: booking.provider_id,
        frequency: booking.frequency,
        start_date: booking.start_date,
        end_date: booking.end_date,
        start_time: booking.start_time,
        end_time: booking.end_time,
        duration_hours: booking.duration_hours,
        hourly_rate: booking.hourly_rate,
        is_active: booking.is_active,
        next_booking_date: booking.next_booking_date,
        total_bookings_created: booking.total_bookings_created || 0,
        special_instructions: booking.special_instructions,
        days_of_week: booking.days_of_week || [],
        created_at: booking.created_at,
        provider_info: booking.profiles ? {
          full_name: booking.profiles.full_name,
          avatar_url: booking.profiles.avatar_url,
          rating: booking.profiles.rating,
          specialties: booking.profiles.specialties || []
        } : undefined
      }))

      setRecurringBookings(transformed)
    } catch (error) {
      console.error('Error loading recurring bookings:', error)
    }
  }

  const createRecurringBooking = async () => {
    if (!user || !newRecurring.provider_id || !newRecurring.start_date || !newRecurring.start_time) {
      return
    }

    setProcessing(true)
    try {
      const startTime = new Date(`2000-01-01T${newRecurring.start_time}`)
      const endTime = new Date(`2000-01-01T${newRecurring.end_time}`)
      const durationHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60)

      const { error } = await supabase
        .from('recurring_bookings')
        .insert({
          client_id: user.id,
          provider_id: newRecurring.provider_id,
          frequency: newRecurring.frequency,
          start_date: newRecurring.start_date,
          end_date: newRecurring.end_date || null,
          start_time: newRecurring.start_time,
          end_time: newRecurring.end_time,
          duration_hours: durationHours,
          hourly_rate: parseFloat(newRecurring.hourly_rate),
          is_active: true,
          next_booking_date: newRecurring.start_date,
          days_of_week: newRecurring.days_of_week,
          special_instructions: newRecurring.special_instructions
        })

      if (error) throw error

      await loadRecurringBookings()
      setShowCreateModal(false)
      setNewRecurring({
        provider_id: '',
        frequency: 'weekly',
        start_date: '',
        end_date: '',
        start_time: '',
        end_time: '',
        hourly_rate: '',
        days_of_week: [],
        special_instructions: ''
      })
    } catch (error) {
      console.error('Error creating recurring booking:', error)
    } finally {
      setProcessing(false)
    }
  }

  const toggleRecurringStatus = async (bookingId: string, currentStatus: boolean) => {
    setProcessing(true)
    try {
      const { error } = await supabase
        .from('recurring_bookings')
        .update({ is_active: !currentStatus })
        .eq('id', bookingId)

      if (error) throw error

      await loadRecurringBookings()
    } catch (error) {
      console.error('Error toggling recurring status:', error)
    } finally {
      setProcessing(false)
    }
  }

  const deleteRecurringBooking = async (bookingId: string) => {
    if (!confirm('Are you sure you want to delete this recurring booking? This action cannot be undone.')) {
      return
    }

    setProcessing(true)
    try {
      const { error } = await supabase
        .from('recurring_bookings')
        .delete()
        .eq('id', bookingId)

      if (error) throw error

      await loadRecurringBookings()
    } catch (error) {
      console.error('Error deleting recurring booking:', error)
    } finally {
      setProcessing(false)
    }
  }

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case 'daily': return 'Daily'
      case 'weekly': return 'Weekly'
      case 'biweekly': return 'Every 2 weeks'
      case 'monthly': return 'Monthly'
      default: return frequency
    }
  }

  const getDaysOfWeekText = (days: number[]) => {
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    return days.map(day => dayNames[day]).join(', ')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading recurring bookings...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <Repeat className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Recurring Bookings Unavailable</h1>
          <p className="text-gray-600 mb-6">Please sign in to manage your recurring bookings.</p>
          <button
            onClick={() => window.location.href = '/auth'}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Recurring Bookings</h1>
              <p className="text-gray-600">Set up and manage your recurring care appointments</p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2"
            >
              <Repeat className="w-4 h-4" />
              Create Recurring Booking
            </button>
          </div>
        </div>

        {/* Filter */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-gray-700">Filter:</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
            >
              <option value="all">All Bookings</option>
              <option value="active">Active</option>
              <option value="paused">Paused</option>
            </select>
          </div>
        </div>

        {/* Recurring Bookings List */}
        <div className="space-y-4">
          {recurringBookings.length > 0 ? (
            recurringBookings.map((booking) => (
              <div key={booking.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${booking.is_active ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Repeat className={`w-5 h-5 ${booking.is_active ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {booking.provider_info?.full_name || 'Unknown Provider'}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                        booking.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {booking.is_active ? 'Active' : 'Paused'}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => toggleRecurringStatus(booking.id, booking.is_active)}
                      disabled={processing}
                      className={`p-2 rounded-lg transition-colors ${
                        booking.is_active 
                          ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200' 
                          : 'bg-green-100 text-green-600 hover:bg-green-200'
                      }`}
                      title={booking.is_active ? 'Pause' : 'Resume'}
                    >
                      {booking.is_active ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    </button>
                    <button
                      onClick={() => setEditingBooking(booking)}
                      className="p-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors"
                      title="Edit"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => deleteRecurringBooking(booking.id)}
                      disabled={processing}
                      className="p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors"
                      title="Delete"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <Repeat className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {getFrequencyText(booking.frequency)}
                      {booking.days_of_week.length > 0 && ` (${getDaysOfWeekText(booking.days_of_week)})`}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {booking.start_time} - {booking.end_time} ({booking.duration_hours}h)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      ${booking.hourly_rate}/hour (${(booking.hourly_rate * booking.duration_hours).toFixed(2)} per session)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      Next: {new Date(booking.next_booking_date).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-700">
                      <span className="font-medium">Started:</span> {new Date(booking.start_date).toLocaleDateString()}
                      {booking.end_date && (
                        <>
                          <span className="mx-2">•</span>
                          <span className="font-medium">Ends:</span> {new Date(booking.end_date).toLocaleDateString()}
                        </>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      {booking.total_bookings_created} sessions created
                    </div>
                  </div>
                  {booking.special_instructions && (
                    <div className="mt-2 text-sm text-gray-600">
                      <span className="font-medium">Instructions:</span> {booking.special_instructions}
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="bg-white rounded-lg shadow-sm p-12 text-center">
              <Repeat className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No recurring bookings</h3>
              <p className="text-gray-600 mb-6">
                {filterStatus !== 'all' ? 
                  `No ${filterStatus} recurring bookings found.` : 
                  'Set up recurring appointments for regular care services.'
                }
              </p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
              >
                Create Your First Recurring Booking
              </button>
            </div>
          )}
        </div>

        {/* Create Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-gray-900">Create Recurring Booking</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input
                      type="date"
                      value={newRecurring.start_date}
                      onChange={(e) => setNewRecurring(prev => ({ ...prev, start_date: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Date (Optional)</label>
                    <input
                      type="date"
                      value={newRecurring.end_date}
                      onChange={(e) => setNewRecurring(prev => ({ ...prev, end_date: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                    <input
                      type="time"
                      value={newRecurring.start_time}
                      onChange={(e) => setNewRecurring(prev => ({ ...prev, start_time: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                    <input
                      type="time"
                      value={newRecurring.end_time}
                      onChange={(e) => setNewRecurring(prev => ({ ...prev, end_time: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
                    <select
                      value={newRecurring.frequency}
                      onChange={(e) => setNewRecurring(prev => ({ ...prev, frequency: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    >
                      <option value="weekly">Weekly</option>
                      <option value="biweekly">Every 2 weeks</option>
                      <option value="monthly">Monthly</option>
                      <option value="daily">Daily</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Hourly Rate ($)</label>
                    <input
                      type="number"
                      step="0.01"
                      value={newRecurring.hourly_rate}
                      onChange={(e) => setNewRecurring(prev => ({ ...prev, hourly_rate: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Special Instructions</label>
                  <textarea
                    value={newRecurring.special_instructions}
                    onChange={(e) => setNewRecurring(prev => ({ ...prev, special_instructions: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    placeholder="Any special instructions for the recurring bookings..."
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-8">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={createRecurringBooking}
                  disabled={processing || !newRecurring.start_date || !newRecurring.start_time}
                  className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
                >
                  {processing ? 'Creating...' : 'Create Recurring Booking'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default BookingRecurringPage
