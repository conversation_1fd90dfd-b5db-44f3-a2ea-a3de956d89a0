import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Bell, Check, X, Clock, User, Calendar, Settings, Filter, Search, Trash2 } from 'lucide-react';
import { format, parseISO, isToday, isYesterday, formatDistanceToNow } from 'date-fns';

interface BookingNotification {
  id: string;
  user_id: string;
  booking_id: string;
  type: 'booking_confirmed' | 'booking_cancelled' | 'booking_reminder' | 'review_request';
  title: string;
  message: string;
  is_read: boolean;
  created_at: string;
  provider_name?: string;
  service_type?: string;
}

const BookingNotificationsPage: React.FC = () => {
  const navigate = useNavigate();
  
  const [notifications, setNotifications] = useState<BookingNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const filterOptions = [
    { value: 'all', label: 'All Notifications' },
    { value: 'unread', label: 'Unread' },
    { value: 'booking_confirmed', label: 'Confirmations' },
    { value: 'booking_reminder', label: 'Reminders' }
  ];

  useEffect(() => {
    const fetchNotifications = async () => {
      setLoading(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setError("Please sign in to view notifications.");
          setLoading(false);
          return;
        }

        // Mock notifications with real structure
        const mockNotifications: BookingNotification[] = [
          {
            id: '1',
            user_id: user.id,
            booking_id: '1',
            type: 'booking_confirmed',
            title: 'Booking Confirmed',
            message: 'Your booking with Sarah Johnson has been confirmed for tomorrow at 2:00 PM.',
            is_read: false,
            created_at: new Date().toISOString(),
            provider_name: 'Sarah Johnson',
            service_type: 'Senior Companion'
          },
          {
            id: '2',
            user_id: user.id,
            booking_id: '2',
            type: 'booking_reminder',
            title: 'Upcoming Appointment',
            message: 'Reminder: You have an appointment with Michael Chen in 2 hours.',
            is_read: false,
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            provider_name: 'Michael Chen',
            service_type: 'Companion Care'
          },
          {
            id: '3',
            user_id: user.id,
            booking_id: '3',
            type: 'review_request',
            title: 'Rate Your Experience',
            message: 'How was your experience with Lisa Wong? Please leave a review.',
            is_read: true,
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            provider_name: 'Lisa Wong',
            service_type: 'Care Checker'
          }
        ];

        setNotifications(mockNotifications);
        console.log('Notifications loaded successfully:', mockNotifications);

      } catch (err: any) {
        console.error("Error fetching notifications:", err.message);
        setError(err.message || "Failed to load notifications.");
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  const markAsRead = async (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, is_read: true }
          : notification
      )
    );
  };

  const markAllAsRead = async () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, is_read: true }))
    );
  };

  const deleteNotification = async (notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'booking_confirmed':
        return <Check className="h-5 w-5 text-green-600" />;
      case 'booking_cancelled':
        return <X className="h-5 w-5 text-red-600" />;
      case 'booking_reminder':
        return <Clock className="h-5 w-5 text-orange-600" />;
      case 'review_request':
        return <User className="h-5 w-5 text-purple-600" />;
      default:
        return <Bell className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTimeDisplay = (createdAt: string) => {
    const date = parseISO(createdAt);
    if (isToday(date)) {
      return formatDistanceToNow(date, { addSuffix: true });
    } else if (isYesterday(date)) {
      return 'Yesterday';
    } else {
      return format(date, 'MMM d, yyyy');
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    const matchesFilter = filter === 'all' || 
                         (filter === 'unread' && !notification.is_read) ||
                         notification.type === filter;
    
    const matchesSearch = searchTerm === '' ||
                         notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  const unreadCount = notifications.filter(n => !n.is_read).length;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading notifications...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <Bell className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Notifications</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="flex gap-3">
              <button
                onClick={() => navigate('/dashboard')}
                className="flex-1 bg-var(--logo-green) text-white px-4 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
              >
                Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Bell className="h-8 w-8 text-var(--logo-green) mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
                <p className="text-gray-600">
                  {unreadCount > 0 ? `${unreadCount} unread notification${unreadCount > 1 ? 's' : ''}` : 'All caught up!'}
                </p>
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </button>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="flex items-center px-4 py-2 bg-var(--logo-green) text-white rounded-xl hover:bg-green-600 transition-colors"
                >
                  Mark All Read
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Filters */}
        {showFilters && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter Notifications</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                >
                  {filterOptions.map((option) => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Notifications List */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              {filteredNotifications.length} notification{filteredNotifications.length !== 1 ? 's' : ''}
            </h3>
          </div>

          {filteredNotifications.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-6 hover:bg-gray-50 transition-colors ${
                    !notification.is_read ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className={`flex-shrink-0 p-2 rounded-xl ${
                        !notification.is_read ? 'bg-white' : 'bg-gray-100'
                      }`}>
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className={`text-sm font-medium ${
                            !notification.is_read ? 'text-gray-900' : 'text-gray-700'
                          }`}>
                            {notification.title}
                          </h4>
                          {!notification.is_read && (
                            <span className="h-2 w-2 bg-blue-600 rounded-full"></span>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">{notification.message}</p>
                        
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>{getTimeDisplay(notification.created_at)}</span>
                          {notification.provider_name && (
                            <span>• {notification.provider_name}</span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Link
                        to={`/booking/${notification.booking_id}`}
                        className="text-xs text-var(--logo-green) hover:text-green-600 font-medium"
                      >
                        View
                      </Link>
                      
                      {!notification.is_read && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                        >
                          Read
                        </button>
                      )}
                      
                      <button
                        onClick={() => deleteNotification(notification.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-12 text-center">
              <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
              <p className="text-gray-600">You're all caught up!</p>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-blue-50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/my-bookings"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              View All Bookings
            </Link>
            <Link
              to="/booking-analytics"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              Booking Analytics
            </Link>
            <Link
              to="/dashboard"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingNotificationsPage;
