import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Users, Search, Filter, MoreVertical, Mail, Phone, Calendar, Shield, UserPlus, UserMinus, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface GroupMember {
  user_id: string
  full_name: string
  avatar_url?: string
  email?: string
  phone?: string
  role: 'admin' | 'moderator' | 'member'
  joined_at: string
  last_active?: string
  location?: string
  bio?: string
  verified: boolean
  contributions: {
    messages: number
    events_created: number
    events_attended: number
  }
}

interface CareGroupInfo {
  id: string
  name: string
  member_count: number
  group_type: string
}

const CareGroupMembersPage: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>()
  const navigate = useNavigate()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [careGroup, setCareGroup] = useState<CareGroupInfo | null>(null)
  const [members, setMembers] = useState<GroupMember[]>([])
  const [filteredMembers, setFilteredMembers] = useState<GroupMember[]>([])
  const [userRole, setUserRole] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [inviteEmail, setInviteEmail] = useState('')
  const [selectedMember, setSelectedMember] = useState<GroupMember | null>(null)
  const [showMemberMenu, setShowMemberMenu] = useState<string | null>(null)

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user && groupId) {
        await loadGroupInfo()
        await loadMembers()
        await checkUserRole()
      }
      setLoading(false)
    }
    getUser()
  }, [groupId])

  useEffect(() => {
    filterMembers()
  }, [members, searchTerm, roleFilter])

  const loadGroupInfo = async () => {
    if (!groupId) return

    try {
      const { data, error } = await supabase
        .from('care_groups')
        .select('id, name, member_count, group_type')
        .eq('id', groupId)
        .single()

      if (error) throw error
      setCareGroup(data)
    } catch (error) {
      console.error('Error loading group info:', error)
    }
  }

  const loadMembers = async () => {
    if (!groupId) return

    try {
      const { data: membersData, error: membersError } = await supabase
        .from('care_group_members')
        .select(`
          user_id, role, joined_at,
          profiles!care_group_members_user_id_fkey(
            full_name, avatar_url, email, phone, location, bio, verified, last_sign_in_at
          )
        `)
        .eq('group_id', groupId)
        .order('joined_at', { ascending: true })

      if (membersError) throw membersError

      // Get member activity stats
      const memberIds = (membersData || []).map(m => m.user_id)
      const { data: activityData, error: activityError } = await supabase
        .from('care_group_activity')
        .select('user_id, activity_type')
        .eq('group_id', groupId)
        .in('user_id', memberIds)

      if (activityError) throw activityError

      // Calculate contributions
      const contributions = memberIds.reduce((acc, userId) => {
        const userActivity = (activityData || []).filter(a => a.user_id === userId)
        acc[userId] = {
          messages: userActivity.filter(a => a.activity_type === 'message').length,
          events_created: userActivity.filter(a => a.activity_type === 'event').length,
          events_attended: 0 // Would need separate events attendance table
        }
        return acc
      }, {} as Record<string, any>)

      setMembers((membersData || []).map(member => ({
        user_id: member.user_id,
        full_name: member.profiles?.full_name || 'Unknown User',
        avatar_url: member.profiles?.avatar_url,
        email: member.profiles?.email,
        phone: member.profiles?.phone,
        role: member.role,
        joined_at: member.joined_at,
        last_active: member.profiles?.last_sign_in_at,
        location: member.profiles?.location,
        bio: member.profiles?.bio,
        verified: member.profiles?.verified || false,
        contributions: contributions[member.user_id] || { messages: 0, events_created: 0, events_attended: 0 }
      })))
    } catch (error) {
      console.error('Error loading members:', error)
    }
  }

  const checkUserRole = async () => {
    if (!user || !groupId) return

    try {
      const { data, error } = await supabase
        .from('care_group_members')
        .select('role')
        .eq('group_id', groupId)
        .eq('user_id', user.id)
        .single()

      if (error) throw error
      setUserRole(data.role)
    } catch (error) {
      console.error('Error checking user role:', error)
    }
  }

  const filterMembers = () => {
    let filtered = members

    if (searchTerm) {
      filtered = filtered.filter(member =>
        member.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.location?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(member => member.role === roleFilter)
    }

    setFilteredMembers(filtered)
  }

  const updateMemberRole = async (memberId: string, newRole: 'admin' | 'moderator' | 'member') => {
    if (!confirm(`Change member role to ${newRole}?`)) return

    setProcessing(true)
    try {
      const { error } = await supabase
        .from('care_group_members')
        .update({ role: newRole })
        .eq('group_id', groupId)
        .eq('user_id', memberId)

      if (error) throw error
      await loadMembers()
      alert('Member role updated successfully!')
    } catch (error) {
      console.error('Error updating member role:', error)
      alert('Error updating member role. Please try again.')
    } finally {
      setProcessing(false)
      setShowMemberMenu(null)
    }
  }

  const removeMember = async (memberId: string, memberName: string) => {
    if (!confirm(`Remove ${memberName} from the group?`)) return

    setProcessing(true)
    try {
      const { error } = await supabase
        .from('care_group_members')
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', memberId)

      if (error) throw error

      // Update member count
      if (careGroup) {
        await supabase
          .from('care_groups')
          .update({ member_count: Math.max(0, careGroup.member_count - 1) })
          .eq('id', groupId)
      }

      await loadGroupInfo()
      await loadMembers()
      alert('Member removed successfully!')
    } catch (error) {
      console.error('Error removing member:', error)
      alert('Error removing member. Please try again.')
    } finally {
      setProcessing(false)
      setShowMemberMenu(null)
    }
  }

  const sendInvite = async () => {
    if (!inviteEmail.trim()) return

    setProcessing(true)
    try {
      // Check if user exists
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('id, full_name')
        .eq('email', inviteEmail.trim())
        .single()

      if (userError && userError.code !== 'PGRST116') throw userError

      if (userData) {
        // Check if already a member
        const { data: memberData, error: memberError } = await supabase
          .from('care_group_members')
          .select('user_id')
          .eq('group_id', groupId)
          .eq('user_id', userData.id)
          .single()

        if (memberError && memberError.code !== 'PGRST116') throw memberError

        if (memberData) {
          alert('User is already a member of this group!')
          setInviteEmail('')
          setProcessing(false)
          return
        }

        // Add as member
        const { error: insertError } = await supabase
          .from('care_group_members')
          .insert({
            group_id: groupId,
            user_id: userData.id,
            role: 'member'
          })

        if (insertError) throw insertError

        // Update member count
        if (careGroup) {
          await supabase
            .from('care_groups')
            .update({ member_count: careGroup.member_count + 1 })
            .eq('id', groupId)
        }

        await loadGroupInfo()
        await loadMembers()
        alert(`${userData.full_name} has been added to the group!`)
      } else {
        // Send invitation email (would need email service integration)
        alert('Invitation sent! The user will receive an email to join the group.')
      }

      setInviteEmail('')
      setShowInviteModal(false)
    } catch (error) {
      console.error('Error sending invite:', error)
      alert('Error sending invite. Please try again.')
    } finally {
      setProcessing(false)
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'moderator': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRolePriority = (role: string) => {
    switch (role) {
      case 'admin': return 3
      case 'moderator': return 2
      default: return 1
    }
  }

  const canManageMembers = userRole === 'admin' || userRole === 'moderator'

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading members...</p>
        </div>
      </div>
    )
  }

  if (!careGroup) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Group Not Found</h1>
          <p className="text-gray-600 mb-6">The care group you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/care-groups')}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Browse Groups
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate(`/care-groups/${groupId}`)}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Group
          </button>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-logo-green rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Group Members</h1>
                <p className="text-gray-600">{careGroup.name} • {careGroup.member_count} members</p>
              </div>
            </div>
            
            {canManageMembers && (
              <button
                onClick={() => setShowInviteModal(true)}
                className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2"
              >
                <UserPlus className="w-4 h-4" />
                Invite Member
              </button>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search members by name, email, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-logo-green focus:border-logo-green"
              >
                <option value="all">All Roles</option>
                <option value="admin">Admin</option>
                <option value="moderator">Moderator</option>
                <option value="member">Member</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            Showing {filteredMembers.length} of {members.length} members
          </div>
        </div>

        {/* Members List */}
        <div className="space-y-4">
          {filteredMembers.map((member) => (
            <div key={member.user_id} className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4 flex-1">
                  {member.avatar_url ? (
                    <img 
                      src={member.avatar_url} 
                      alt={member.full_name}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                      <Users className="w-8 h-8 text-gray-600" />
                    </div>
                  )}
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{member.full_name}</h3>
                      {member.verified && (
                        <CheckCircle className="w-5 h-5 text-logo-green" />
                      )}
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(member.role)}`}>
                        {member.role}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                      {member.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4" />
                          {member.email}
                        </div>
                      )}
                      {member.location && (
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          {member.location}
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Joined {new Date(member.joined_at).toLocaleDateString()}
                      </div>
                      {member.last_active && (
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          Active {new Date(member.last_active).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                    
                    {member.bio && (
                      <p className="mt-2 text-gray-700">{member.bio}</p>
                    )}
                    
                    <div className="mt-3 flex items-center gap-6 text-sm text-gray-600">
                      <span>{member.contributions.messages} messages</span>
                      <span>{member.contributions.events_created} events created</span>
                      <span>{member.contributions.events_attended} events attended</span>
                    </div>
                  </div>
                </div>
                
                {canManageMembers && member.user_id !== user?.id && getRolePriority(userRole) > getRolePriority(member.role) && (
                  <div className="relative">
                    <button
                      onClick={() => setShowMemberMenu(showMemberMenu === member.user_id ? null : member.user_id)}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    >
                      <MoreVertical className="w-4 h-4" />
                    </button>
                    
                    {showMemberMenu === member.user_id && (
                      <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                        <div className="py-1">
                          {member.role !== 'moderator' && userRole === 'admin' && (
                            <button
                              onClick={() => updateMemberRole(member.user_id, 'moderator')}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                            >
                              <Shield className="w-4 h-4" />
                              Make Moderator
                            </button>
                          )}
                          {member.role !== 'admin' && userRole === 'admin' && (
                            <button
                              onClick={() => updateMemberRole(member.user_id, 'admin')}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                            >
                              <Shield className="w-4 h-4" />
                              Make Admin
                            </button>
                          )}
                          {member.role !== 'member' && (
                            <button
                              onClick={() => updateMemberRole(member.user_id, 'member')}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                            >
                              <Users className="w-4 h-4" />
                              Make Member
                            </button>
                          )}
                          <button
                            onClick={() => removeMember(member.user_id, member.full_name)}
                            className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                          >
                            <UserMinus className="w-4 h-4" />
                            Remove Member
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {filteredMembers.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No members found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
          </div>
        )}

        {/* Invite Modal */}
        {showInviteModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Invite New Member</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                  <input
                    type="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="Enter email address"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                  />
                </div>
                <div className="flex gap-3">
                  <button
                    onClick={() => {
                      setShowInviteModal(false)
                      setInviteEmail('')
                    }}
                    className="flex-1 bg-gray-100 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={sendInvite}
                    disabled={processing || !inviteEmail.trim()}
                    className="flex-1 bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
                  >
                    {processing ? 'Sending...' : 'Send Invite'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CareGroupMembersPage
