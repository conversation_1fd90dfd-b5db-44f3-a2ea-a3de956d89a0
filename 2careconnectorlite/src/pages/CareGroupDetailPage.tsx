import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { Users, Calendar, MessageSquare, Settings, MapPin, UserPlus, CheckCircle, AlertCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface CareGroupDetail {
  id: string
  name: string
  description: string
  group_type: 'public' | 'private' | 'support'
  location: string
  member_count: number
  max_members: number
  created_at: string
  tags: string[]
  members: Array<{
    user_id: string
    full_name: string
    avatar_url?: string
    role: 'admin' | 'moderator' | 'member'
    joined_at: string
  }>
  recent_activity: Array<{
    id: string
    type: string
    content: string
    user_name: string
    created_at: string
  }>
}

const CareGroupDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [careGroup, setCareGroup] = useState<CareGroupDetail | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [userMembership, setUserMembership] = useState<any>(null)
  const [debugInfo, setDebugInfo] = useState<string>('Initializing...')

  useEffect(() => {
    const getUser = async () => {
      console.log('🔍 CareGroupDetailPage: useEffect triggered - Starting getUser...')
      console.log('🔍 CareGroupDetailPage: URL id parameter:', id)
      
      const { data: { user } } = await supabase.auth.getUser()
      console.log('🔍 CareGroupDetailPage: Authentication result - User:', user ? 'authenticated' : 'not authenticated')
      console.log('🔍 CareGroupDetailPage: User ID:', user?.id || 'null')
      
      setUser(user)
      
      console.log('🔍 CareGroupDetailPage: Checking conditions - user:', !!user, 'id:', !!id)
      
      if (user && id) {
        console.log('✅ CareGroupDetailPage: Both user and id present - calling loadCareGroupDetail...')
        await loadCareGroupDetail()
        await checkUserMembership()
      } else {
        console.log('❌ CareGroupDetailPage: Missing required data - user:', !!user, 'id:', !!id)
        if (!user) console.log('❌ CareGroupDetailPage: AUTHENTICATION ISSUE - User not authenticated')
        if (!id) console.log('❌ CareGroupDetailPage: ROUTING ISSUE - ID parameter missing')
      }
      
      console.log('🔍 CareGroupDetailPage: Setting loading to false...')
      setLoading(false)
    }
    getUser()
  }, [id])

  const loadCareGroupDetail = async () => {
    if (!id) {
      setDebugInfo('❌ No ID parameter provided')
      return
    }

    setDebugInfo('🔍 Starting loadCareGroupDetail...')
    console.log('🔍 CareGroupDetailPage: Starting loadCareGroupDetail with ID:', id)

    try {
      setDebugInfo('🔍 STEP 1: Querying care_groups table...')
      console.log('🔍 CareGroupDetailPage: STEP 1 - Querying care_groups table with ID:', id)
      const { data: groupData, error: groupError } = await supabase
        .from('care_groups')
        .select('*')
        .eq('id', id)
        .single()

      console.log('🔍 CareGroupDetailPage: STEP 1 RESULT - Data:', groupData, 'Error:', groupError)

      if (groupError) {
        setDebugInfo(`❌ STEP 1 FAILED: ${groupError.message || 'care_groups query failed'}`)
        console.error('❌ CareGroupDetailPage: STEP 1 FAILED - care_groups query failed:', groupError)
        throw groupError
      }

      setDebugInfo('✅ STEP 1 SUCCESS: care_groups query completed')
      console.log('✅ CareGroupDetailPage: STEP 1 SUCCESS - care_groups query completed successfully')
      
      setDebugInfo('🔍 STEP 2: Querying care_group_members table...')
      console.log('🔍 CareGroupDetailPage: STEP 2 - Querying care_group_members table with group_id:', id)
      
      // First get members without foreign key reference
      const { data: membersData, error: membersError } = await supabase
        .from('care_group_members')
        .select('user_id, role, joined_at')
        .eq('group_id', id)

      console.log('🔍 CareGroupDetailPage: STEP 2A RESULT - Members Data:', membersData, 'Error:', membersError)

      if (membersError) {
        setDebugInfo(`❌ STEP 2A FAILED: ${membersError.message || 'care_group_members query failed'}`)
        console.error('❌ CareGroupDetailPage: STEP 2A FAILED - care_group_members query failed:', membersError)
        throw membersError
      }

      setDebugInfo('🔍 STEP 2B: Querying profiles for member details...')
      console.log('🔍 CareGroupDetailPage: STEP 2B - Querying profiles for member details')
      
      // Get user IDs for profile lookup
      const userIds = (membersData || []).map(member => member.user_id)
      
      // Get profiles for all member user IDs
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name, avatar_url')
        .in('id', userIds)

      console.log('🔍 CareGroupDetailPage: STEP 2B RESULT - Profiles Data:', profilesData, 'Error:', profilesError)

      if (profilesError) {
        setDebugInfo(`❌ STEP 2B FAILED: ${profilesError.message || 'profiles query failed'}`)
        console.error('❌ CareGroupDetailPage: STEP 2B FAILED - profiles query failed:', profilesError)
        throw profilesError
      }

      setDebugInfo('✅ STEP 2 SUCCESS: care_group_members query completed')
      console.log('✅ CareGroupDetailPage: STEP 2 SUCCESS - care_group_members query completed successfully')
      
      setDebugInfo('🔍 STEP 3: Querying care_group_activity table (optional)...')
      console.log('🔍 CareGroupDetailPage: STEP 3 - Attempting to query care_group_activity table with group_id:', id)

      // Try to get activity data - make it optional if table doesn't exist
      let activityData = []
      let activityProfilesData = []
      
      try {
        const { data: activityResult, error: activityError } = await supabase
          .from('care_group_activity')
          .select('id, activity_type, content, created_at, user_id')
          .eq('group_id', id)
          .order('created_at', { ascending: false })
          .limit(10)

        console.log('🔍 CareGroupDetailPage: STEP 3A RESULT - Activity Data:', activityResult, 'Error:', activityError)

        if (activityError) {
          // If table doesn't exist, log but don't throw - continue without activity
          if (activityError.message?.includes('does not exist')) {
            setDebugInfo('⚠️ STEP 3A SKIPPED: care_group_activity table does not exist (continuing without activity)')
            console.warn('⚠️ CareGroupDetailPage: STEP 3A SKIPPED - care_group_activity table does not exist, continuing without activity')
          } else {
            setDebugInfo(`❌ STEP 3A FAILED: ${activityError.message || 'care_group_activity query failed'}`)
            console.error('❌ CareGroupDetailPage: STEP 3A FAILED - care_group_activity query failed:', activityError)
            throw activityError
          }
        } else {
          activityData = activityResult || []
          
          if (activityData.length > 0) {
            setDebugInfo('🔍 STEP 3B: Querying profiles for activity users...')
            console.log('🔍 CareGroupDetailPage: STEP 3B - Querying profiles for activity users')
            
            // Get activity user IDs for profile lookup
            const activityUserIds = activityData.map(activity => activity.user_id).filter(Boolean)
            
            // Get profiles for activity users
            if (activityUserIds.length > 0) {
              const { data: profilesResult, error: activityProfilesError } = await supabase
                .from('profiles')
                .select('id, full_name')
                .in('id', activityUserIds)

              console.log('🔍 CareGroupDetailPage: STEP 3B RESULT - Activity Profiles Data:', profilesResult, 'Error:', activityProfilesError)

              if (activityProfilesError) {
                setDebugInfo(`❌ STEP 3B FAILED: ${activityProfilesError.message || 'activity profiles query failed'}`)
                console.error('❌ CareGroupDetailPage: STEP 3B FAILED - activity profiles query failed:', activityProfilesError)
                throw activityProfilesError
              }
              
              activityProfilesData = profilesResult || []
            }
          }
        }
      } catch (error) {
        // If it's a table existence error, continue without activity, otherwise re-throw
        if (error.message?.includes('does not exist')) {
          setDebugInfo('⚠️ STEP 3 SKIPPED: care_group_activity table does not exist (continuing without activity)')
          console.warn('⚠️ CareGroupDetailPage: STEP 3 SKIPPED - care_group_activity table does not exist, continuing without activity')
        } else {
          throw error
        }
      }

      setDebugInfo('✅ STEP 3 SUCCESS: care_group_activity query completed')
      console.log('✅ CareGroupDetailPage: STEP 3 SUCCESS - care_group_activity query completed successfully')
      
      setDebugInfo('🔍 STEP 4: Setting careGroup state...')
      console.log('🔍 CareGroupDetailPage: STEP 4 - All queries successful, calling setCareGroup...')

      setCareGroup({
        id: groupData.id,
        name: groupData.name,
        description: groupData.description,
        group_type: groupData.group_type,
        location: groupData.location,
        member_count: groupData.member_count,
        max_members: groupData.max_members,
        created_at: groupData.created_at,
        tags: groupData.tags || [],
        members: (membersData || []).map(member => {
          // Find corresponding profile data
          const profile = (profilesData || []).find(p => p.id === member.user_id)
          return {
            user_id: member.user_id,
            full_name: profile?.full_name || 'Unknown User',
            avatar_url: profile?.avatar_url,
            role: member.role,
            joined_at: member.joined_at
          }
        }),
        recent_activity: (activityData || []).map(activity => {
          // Find corresponding profile data for activity user
          const activityProfile = (activityProfilesData || []).find(p => p.id === activity.user_id)
          return {
            id: activity.id,
            type: activity.activity_type,
            content: activity.content,
            user_name: activityProfile?.full_name || 'Unknown User',
            created_at: activity.created_at
          }
        })
      })
    } catch (error) {
      console.error('Error loading care group detail:', error)
    }
  }

  const checkUserMembership = async () => {
    if (!user || !id) return

    try {
      const { data, error } = await supabase
        .from('care_group_members')
        .select('role, joined_at')
        .eq('group_id', id)
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      setUserMembership(data)
    } catch (error) {
      console.error('Error checking membership:', error)
    }
  }

  const joinCareGroup = async () => {
    if (!user || !id || !careGroup) return

    setProcessing(true)
    try {
      const { error } = await supabase
        .from('care_group_members')
        .insert({
          group_id: id,
          user_id: user.id,
          role: 'member'
        })

      if (error) throw error

      await supabase
        .from('care_groups')
        .update({ member_count: careGroup.member_count + 1 })
        .eq('id', id)

      await loadCareGroupDetail()
      await checkUserMembership()
    } catch (error) {
      console.error('Error joining care group:', error)
    } finally {
      setProcessing(false)
    }
  }

  const getGroupTypeColor = (type: string) => {
    switch (type) {
      case 'public': return 'bg-green-100 text-green-800'
      case 'private': return 'bg-blue-100 text-blue-800'
      case 'support': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'moderator': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading care group details...</p>
        </div>
      </div>
    )
  }

  if (!careGroup) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Care Group Not Found</h1>
          <p className="text-gray-600 mb-6">The care group you're looking for doesn't exist or you don't have permission to view it.</p>
          
          {/* TEMPORARY VISUAL DEBUGGING - REMOVE AFTER FIX */}
          <div className="bg-yellow-100 border border-yellow-400 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-bold text-yellow-800 mb-2">🔍 DEBUG INFO:</h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <div><strong>User Status:</strong> {user ? `✅ Authenticated (${user.id})` : '❌ Not Authenticated'}</div>
              <div><strong>ID Parameter:</strong> {id ? `✅ Present (${id})` : '❌ Missing'}</div>
              <div><strong>Loading State:</strong> {loading ? '⏳ Loading' : '✅ Completed'}</div>
              <div><strong>CareGroup State:</strong> {careGroup ? '✅ Loaded' : '❌ Null'}</div>
              <div><strong>Execution Path:</strong> {user && id ? '✅ Should call loadCareGroupDetail' : '❌ Missing required data'}</div>
              <div className="mt-2 p-2 bg-yellow-50 rounded border">
                <strong>Function Progress:</strong> {debugInfo}
              </div>
            </div>
          </div>
          
          <button
            onClick={() => window.location.href = '/care-groups'}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Browse Care Groups
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-6">
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 bg-logo-green rounded-full flex items-center justify-center">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{careGroup.name}</h1>
                  <div className="flex items-center gap-3 mt-2">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGroupTypeColor(careGroup.group_type)}`}>
                      {careGroup.group_type}
                    </span>
                    <span className="text-sm text-gray-600">{careGroup.member_count} members</span>
                    {careGroup.location && (
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <MapPin className="w-4 h-4" />
                        {careGroup.location}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <p className="text-gray-700 mb-4">{careGroup.description}</p>
              {careGroup.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {careGroup.tags.map((tag, index) => (
                    <span key={index} className="bg-gray-100 text-gray-700 text-sm px-2 py-1 rounded">
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
            
            {user && !userMembership && (
              <button
                onClick={joinCareGroup}
                disabled={processing || careGroup.member_count >= careGroup.max_members}
                className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2 disabled:opacity-50"
              >
                <UserPlus className="w-4 h-4" />
                {careGroup.member_count >= careGroup.max_members ? 'Group Full' : 'Join Group'}
              </button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', name: 'Overview', icon: Users },
                { id: 'members', name: 'Members', icon: Users },
                { id: 'activity', name: 'Activity', icon: MessageSquare }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-logo-green text-logo-green'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Group Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <span className="text-sm font-medium text-gray-700">Type</span>
                <div className="mt-1">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGroupTypeColor(careGroup.group_type)}`}>
                    {careGroup.group_type}
                  </span>
                </div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">Members</span>
                <div className="mt-1 text-lg font-semibold text-gray-900">
                  {careGroup.member_count}/{careGroup.max_members}
                </div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">Created</span>
                <div className="mt-1 text-gray-900">
                  {new Date(careGroup.created_at).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'members' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              Members ({careGroup.members.length})
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {careGroup.members.map((member) => (
                <div key={member.user_id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    {member.avatar_url ? (
                      <img 
                        src={member.avatar_url} 
                        alt={member.full_name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                        <Users className="w-6 h-6 text-gray-600" />
                      </div>
                    )}
                    <div>
                      <div className="font-medium text-gray-900">{member.full_name}</div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(member.role)}`}>
                        {member.role}
                      </span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    Joined {new Date(member.joined_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
            {careGroup.recent_activity.length > 0 ? (
              <div className="space-y-4">
                {careGroup.recent_activity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-4 border border-gray-200 rounded-lg">
                    <div className="bg-gray-100 p-2 rounded-full">
                      <MessageSquare className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm text-gray-900">
                        <span className="font-medium">{activity.user_name}</span>
                        {activity.type === 'message' && ' posted a message'}
                        {activity.type === 'member_join' && ' joined the group'}
                      </div>
                      {activity.content && (
                        <div className="text-gray-700 mt-1">{activity.content}</div>
                      )}
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(activity.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No recent activity</p>
            )}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => window.location.href = '/care-groups'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Users className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Browse Groups</div>
              <div className="text-sm text-gray-600">Explore other care groups</div>
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Settings className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Dashboard</div>
              <div className="text-sm text-gray-600">Return to main dashboard</div>
            </button>
            <button
              onClick={() => window.location.href = '/secure-messaging'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <MessageSquare className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Messaging</div>
              <div className="text-sm text-gray-600">Send secure messages</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CareGroupDetailPage
