import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { FileText, Download, Mail, Printer, DollarSign, Calendar, User, Check, Clock, AlertCircle, Lock } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface Invoice {
  id: string
  booking_id: string
  invoice_number: string
  amount: number
  tax_amount: number
  total_amount: number
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  issue_date: string
  due_date: string
  payment_date?: string
  created_at: string
  booking_info?: {
    provider_name: string
    service_date: string
    duration: number
    description: string
  }
}

interface InvoiceLineItem {
  id: string
  description: string
  quantity: number
  rate: number
  amount: number
}

const BookingInvoicePage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [showInvoiceDetail, setShowInvoiceDetail] = useState(false)
  const [filterStatus, setFilterStatus] = useState('all')

  const [invoiceStats, setInvoiceStats] = useState({
    total_invoiced: 0,
    total_paid: 0,
    pending_amount: 0,
    overdue_amount: 0
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user) {
        await loadInvoices()
      }
      setLoading(false)
    }
    getUser()
  }, [filterStatus])

  const loadInvoices = async () => {
    if (!user) return

    try {
      let query = supabase
        .from('invoices')
        .select(`
          id, booking_id, invoice_number, amount, tax_amount, total_amount, 
          status, issue_date, due_date, payment_date, created_at,
          bookings!invoices_booking_id_fkey(
            id, service_date, duration_hours, description,
            profiles!bookings_provider_id_fkey(full_name)
          )
        `)
        .eq('client_id', user.id)
        .order('created_at', { ascending: false })

      if (filterStatus !== 'all') {
        query = query.eq('status', filterStatus)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedInvoices = (data || []).map(invoice => ({
        id: invoice.id,
        booking_id: invoice.booking_id,
        invoice_number: invoice.invoice_number,
        amount: invoice.amount,
        tax_amount: invoice.tax_amount,
        total_amount: invoice.total_amount,
        status: invoice.status,
        issue_date: invoice.issue_date,
        due_date: invoice.due_date,
        payment_date: invoice.payment_date,
        created_at: invoice.created_at,
        booking_info: invoice.bookings ? {
          provider_name: invoice.bookings.profiles?.full_name || 'Unknown Provider',
          service_date: invoice.bookings.service_date,
          duration: invoice.bookings.duration_hours || 0,
          description: invoice.bookings.description || 'Care services'
        } : undefined
      }))

      setInvoices(transformedInvoices)

      // Calculate stats
      const totalInvoiced = transformedInvoices.reduce((sum, inv) => sum + inv.total_amount, 0)
      const totalPaid = transformedInvoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total_amount, 0)
      const pendingAmount = transformedInvoices.filter(inv => inv.status === 'sent').reduce((sum, inv) => sum + inv.total_amount, 0)
      const overdueAmount = transformedInvoices.filter(inv => inv.status === 'overdue').reduce((sum, inv) => sum + inv.total_amount, 0)

      setInvoiceStats({
        total_invoiced: totalInvoiced,
        total_paid: totalPaid,
        pending_amount: pendingAmount,
        overdue_amount: overdueAmount
      })
    } catch (error) {
      console.error('Error loading invoices:', error)
    }
  }

  const downloadInvoice = (invoice: Invoice) => {
    const invoiceContent = `
CARE CONNECTOR INVOICE
=====================

Invoice #: ${invoice.invoice_number}
Issue Date: ${new Date(invoice.issue_date).toLocaleDateString()}
Due Date: ${new Date(invoice.due_date).toLocaleDateString()}

Bill To:
${user?.user_metadata?.full_name || 'Customer'}
${user?.email || ''}

Service Provider:
${invoice.booking_info?.provider_name || 'Unknown Provider'}

Service Details:
Date: ${invoice.booking_info?.service_date ? new Date(invoice.booking_info.service_date).toLocaleDateString() : 'N/A'}
Duration: ${invoice.booking_info?.duration || 0} hours
Description: ${invoice.booking_info?.description || 'Care services'}

Amount Breakdown:
Subtotal: $${invoice.amount.toFixed(2)}
Tax: $${invoice.tax_amount.toFixed(2)}
Total: $${invoice.total_amount.toFixed(2)}

Status: ${invoice.status.toUpperCase()}
${invoice.payment_date ? `Payment Date: ${new Date(invoice.payment_date).toLocaleDateString()}` : ''}

Thank you for using Care Connector!
    `.trim()

    const blob = new Blob([invoiceContent], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `invoice-${invoice.invoice_number}.txt`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const sendInvoiceEmail = async (invoiceId: string) => {
    setProcessing(true)
    try {
      const { error } = await supabase
        .from('invoices')
        .update({ 
          status: 'sent',
          updated_at: new Date().toISOString()
        })
        .eq('id', invoiceId)

      if (error) throw error

      await loadInvoices()
    } catch (error) {
      console.error('Error sending invoice:', error)
    } finally {
      setProcessing(false)
    }
  }

  const markAsPaid = async (invoiceId: string) => {
    setProcessing(true)
    try {
      const { error } = await supabase
        .from('invoices')
        .update({ 
          status: 'paid',
          payment_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', invoiceId)

      if (error) throw error

      await loadInvoices()
    } catch (error) {
      console.error('Error marking as paid:', error)
    } finally {
      setProcessing(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <Check className="w-4 h-4 text-green-600" />
      case 'sent': return <Clock className="w-4 h-4 text-blue-600" />
      case 'overdue': return <AlertCircle className="w-4 h-4 text-red-600" />
      default: return <FileText className="w-4 h-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'sent': return 'bg-blue-100 text-blue-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      default: return 'bg-yellow-100 text-yellow-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading invoices...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header with Navigation */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Invoice Management</h1>
                <p className="text-gray-600 mt-1">View and manage your booking invoices and payments</p>
              </div>
              <Link
                to="/dashboard"
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
        
        {/* Authentication Error State */}
        <div className="auth-error-container">
          <div className="auth-error-content">
            <Lock className="auth-error-icon" />
            <h2 className="auth-error-title">Access Restricted</h2>
            <p className="auth-error-message">Please sign in to view and manage your booking invoices and payments.</p>
            <div className="auth-error-buttons">
              <button
                onClick={() => window.location.href = '/auth'}
                className="auth-error-primary-button"
              >
                Sign In
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="auth-error-secondary-button"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Invoice Management</h1>
              <p className="text-gray-600">View and manage your booking invoices and payments</p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <FileText className="w-8 h-8 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${invoiceStats.total_invoiced.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Total Invoiced</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <Check className="w-8 h-8 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${invoiceStats.total_paid.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Total Paid</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${invoiceStats.pending_amount.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <AlertCircle className="w-8 h-8 text-red-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${invoiceStats.overdue_amount.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Overdue</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-gray-700">Filter by Status:</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
            >
              <option value="all">All Invoices</option>
              <option value="draft">Draft</option>
              <option value="sent">Sent</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        {/* Invoice List */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Invoice History</h3>
          </div>

          <div className="p-6">
            {invoices.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Invoice #</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Provider</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Service Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Due Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {invoices.map((invoice) => (
                      <tr key={invoice.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {invoice.invoice_number}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {invoice.booking_info?.provider_name || 'Unknown Provider'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {invoice.booking_info?.service_date ? 
                            new Date(invoice.booking_info.service_date).toLocaleDateString() : 
                            'N/A'
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                          ${invoice.total_amount.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                            {getStatusIcon(invoice.status)}
                            {invoice.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(invoice.due_date).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => downloadInvoice(invoice)}
                              className="text-logo-green hover:text-green-600 transition-colors"
                              title="Download"
                            >
                              <Download className="w-4 h-4" />
                            </button>
                            {invoice.status === 'draft' && (
                              <button
                                onClick={() => sendInvoiceEmail(invoice.id)}
                                disabled={processing}
                                className="text-blue-600 hover:text-blue-800 transition-colors disabled:opacity-50"
                                title="Send via Email"
                              >
                                <Mail className="w-4 h-4" />
                              </button>
                            )}
                            {(invoice.status === 'sent' || invoice.status === 'overdue') && (
                              <button
                                onClick={() => markAsPaid(invoice.id)}
                                disabled={processing}
                                className="text-green-600 hover:text-green-800 transition-colors disabled:opacity-50"
                                title="Mark as Paid"
                              >
                                <Check className="w-4 h-4" />
                              </button>
                            )}
                            <button
                              onClick={() => {
                                setSelectedInvoice(invoice)
                                setShowInvoiceDetail(true)
                              }}
                              className="text-gray-600 hover:text-gray-800 transition-colors"
                            >
                              View
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
                <p className="text-gray-600">
                  {filterStatus !== 'all' ? 
                    `No invoices with status "${filterStatus}" found.` : 
                    'Your invoices will appear here after bookings are completed.'
                  }
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Invoice Detail Modal */}
        {showInvoiceDetail && selectedInvoice && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-gray-900">Invoice Details</h3>
                <button
                  onClick={() => setShowInvoiceDetail(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
                    <div className="text-lg font-semibold text-gray-900">{selectedInvoice.invoice_number}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedInvoice.status)}`}>
                      {getStatusIcon(selectedInvoice.status)}
                      {selectedInvoice.status}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Issue Date</label>
                    <div className="text-gray-900">{new Date(selectedInvoice.issue_date).toLocaleDateString()}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Due Date</label>
                    <div className="text-gray-900">{new Date(selectedInvoice.due_date).toLocaleDateString()}</div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Service Details</label>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-900">
                      <div><strong>Provider:</strong> {selectedInvoice.booking_info?.provider_name || 'Unknown'}</div>
                      <div><strong>Service Date:</strong> {selectedInvoice.booking_info?.service_date ? new Date(selectedInvoice.booking_info.service_date).toLocaleDateString() : 'N/A'}</div>
                      <div><strong>Duration:</strong> {selectedInvoice.booking_info?.duration || 0} hours</div>
                      <div><strong>Description:</strong> {selectedInvoice.booking_info?.description || 'Care services'}</div>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Amount Breakdown</label>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between py-1">
                      <span>Subtotal:</span>
                      <span>${selectedInvoice.amount.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span>Tax:</span>
                      <span>${selectedInvoice.tax_amount.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between py-1 font-bold border-t border-gray-300 mt-2 pt-2">
                      <span>Total:</span>
                      <span>${selectedInvoice.total_amount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {selectedInvoice.payment_date && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Payment Date</label>
                    <div className="text-gray-900">{new Date(selectedInvoice.payment_date).toLocaleDateString()}</div>
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-3 mt-8">
                <button
                  onClick={() => downloadInvoice(selectedInvoice)}
                  className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Download
                </button>
                <button
                  onClick={() => setShowInvoiceDetail(false)}
                  className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => window.location.href = '/booking-payment'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <DollarSign className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Payment Management</div>
              <div className="text-sm text-gray-600">Manage payment methods and history</div>
            </button>
            <button
              onClick={() => window.location.href = '/my-bookings'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Calendar className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">My Bookings</div>
              <div className="text-sm text-gray-600">View current and past bookings</div>
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <User className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Dashboard</div>
              <div className="text-sm text-gray-600">Return to main dashboard</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingInvoicePage
