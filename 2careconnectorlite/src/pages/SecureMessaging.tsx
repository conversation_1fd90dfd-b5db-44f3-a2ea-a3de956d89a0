import React, { useState } from 'react'
import { Send, Paperclip, Search, Shield, Users, MessageCircle } from 'lucide-react'

export default function SecureMessaging() {
  const [selectedConversation, setSelectedConversation] = useState(0)
  const [newMessage, setNewMessage] = useState('')

  const conversations = [
    {
      id: 1,
      name: "Care Team - Dr. <PERSON>",
      lastMessage: "Lab results look good. Let's schedule follow-up.",
      time: "2 hours ago",
      unread: 2,
      participants: ["<PERSON><PERSON>", "<PERSON>", "Primary Caregiver"],
      type: "medical"
    },
    {
      id: 2,
      name: "Physical Therapy Team",
      lastMessage: "Great progress today! Continue exercises.",
      time: "5 hours ago", 
      unread: 0,
      participants: ["Physical Therapist", "You"],
      type: "therapy"
    },
    {
      id: 3,
      name: "Family Care Group",
      lastMessage: "Medication schedule updated for this week.",
      time: "1 day ago",
      unread: 1,
      participants: ["<PERSON>", "Sister", "Primary Caregiver"],
      type: "family"
    },
    {
      id: 4,
      name: "Alzheimer's Support Circle",
      lastMessage: "Weekly check-in: How is everyone doing?",
      time: "2 days ago",
      unread: 0,
      participants: ["Support Group", "12 members"],
      type: "support"
    }
  ]

  const messages = [
    {
      id: 1,
      sender: "<PERSON>. <PERSON>",
      content: "Hello! I've reviewed the latest lab results and everything looks good. The blood pressure medication seems to be working well.",
      time: "10:30 AM",
      type: "text"
    },
    {
      id: 2,
      sender: "You",
      content: "That's great news! Should we continue with the current dosage?",
      time: "10:45 AM", 
      type: "text"
    },
    {
      id: 3,
      sender: "Dr. Smith",
      content: "Yes, let's continue for another month. I'd like to schedule a follow-up appointment to monitor progress.",
      time: "11:15 AM",
      type: "text"
    },
    {
      id: 4,
      sender: "Primary Caregiver",
      content: "I can help coordinate the appointment. What days work best?",
      time: "11:30 AM",
      type: "text"
    }
  ]

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      // In a real app, this would send the message
      setNewMessage('')
    }
  }

  const getConversationIcon = (type: string) => {
    switch (type) {
      case 'medical': return '🩺'
      case 'therapy': return '🏃‍♀️'
      case 'family': return '👨‍👩‍👧‍👦'
      case 'support': return '🤝'
      default: return '💬'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Secure Messaging</h1>
              <p className="mt-2 text-gray-600 flex items-center">
                <Shield className="w-4 h-4 mr-2" />
                HIPAA-compliant communication with your care team
              </p>
            </div>
            <button className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 flex items-center">
              <MessageCircle className="w-4 h-4 mr-2" />
              New Conversation
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg overflow-hidden h-[600px] flex">
          {/* Conversations List */}
          <div className="w-1/3 border-r border-gray-200 flex flex-col">
            {/* Search */}
            <div className="p-4 border-b border-gray-200">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search conversations..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
                />
              </div>
            </div>

            {/* Conversation List */}
            <div className="flex-1 overflow-y-auto">
              {conversations.map((conversation, index) => (
                <div
                  key={conversation.id}
                  className={`p-4 cursor-pointer hover:bg-gray-50 border-b border-gray-100 ${
                    selectedConversation === index ? 'bg-teal-50 border-l-4 border-l-teal-500' : ''
                  }`}
                  onClick={() => setSelectedConversation(index)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="text-2xl">{getConversationIcon(conversation.type)}</div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {conversation.name}
                        </p>
                        <p className="text-sm text-gray-600 truncate">
                          {conversation.lastMessage}
                        </p>
                        <div className="flex items-center mt-1">
                          <Users className="w-3 h-3 text-gray-400 mr-1" />
                          <p className="text-xs text-gray-400">{conversation.participants.length} members</p>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-400">{conversation.time}</p>
                      {conversation.unread > 0 && (
                        <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-teal-600 rounded-full mt-1">
                          {conversation.unread}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Message Area */}
          <div className="flex-1 flex flex-col">
            {/* Conversation Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {conversations[selectedConversation].name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {conversations[selectedConversation].participants.join(', ')}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                    Encrypted
                  </span>
                  <button className="text-gray-400 hover:text-gray-600">
                    <Users className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.sender === 'You' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.sender === 'You' 
                      ? 'bg-teal-600 text-white' 
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    {message.sender !== 'You' && (
                      <p className="text-xs font-medium text-gray-600 mb-1">
                        {message.sender}
                      </p>
                    )}
                    <p className="text-sm">{message.content}</p>
                    <p className={`text-xs mt-1 ${
                      message.sender === 'You' ? 'text-teal-100' : 'text-gray-500'
                    }`}>
                      {message.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <button className="text-gray-400 hover:text-gray-600">
                  <Paperclip className="w-5 h-5" />
                </button>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your secure message..."
                  className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500"
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                />
                <button
                  onClick={handleSendMessage}
                  className="bg-teal-600 text-white p-2 rounded-lg hover:bg-teal-700"
                >
                  <Send className="w-5 h-5" />
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                🔒 All messages are encrypted and HIPAA-compliant
              </p>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-6 bg-teal-50 border border-teal-200 rounded-lg p-4">
          <div className="flex items-start">
            <Shield className="w-5 h-5 text-teal-600 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-teal-800">Secure Communication</h3>
              <p className="text-sm text-teal-700 mt-1">
                All messages are end-to-end encrypted and stored securely. Only authorized care team members can access this conversation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
