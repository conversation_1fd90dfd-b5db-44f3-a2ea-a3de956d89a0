import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { dataService } from '../lib/dataService'
import { supabase } from '../lib/supabase'
import { ArrowLeft, Star, MapPin, Clock, Calendar, MessageCircle, CheckCircle } from 'lucide-react'

interface Provider {
  id: string
  name: string
  bio: string
  location: string
  specialties: string[]
  verified: boolean
  provider_type: string
  hourly_rate?: number
  years_experience?: number
  profile_image?: string
  rating?: number
  reviews_count?: number
  availability?: string[]
}

export default function ProviderProfile() {
  const { providerId, providerType } = useParams()
  const navigate = useNavigate()
  const [provider, setProvider] = useState<Provider | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [bookingStep, setBookingStep] = useState(1)
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([])
  const [paymentMethod, setPaymentMethod] = useState('')

  // Generate dynamic time slots based on business hours
  const generateTimeSlots = () => {
    const slots = []
    // Business hours: 9 AM to 6 PM, excluding lunch hour (12-1 PM)
    for (let hour = 9; hour <= 17; hour++) {
      if (hour !== 12) { // Skip lunch hour
        const time12 = hour > 12 ? `${hour - 12}:00 PM` : `${hour}:00 AM`
        slots.push(time12)
      }
    }
    return slots
  }


  useEffect(() => {
    fetchProvider()
  }, [providerId, providerType])

  useEffect(() => {
    // Set available time slots when component mounts
    setAvailableTimeSlots(generateTimeSlots())
  }, [])


  const fetchProvider = async () => {
    if (!providerId || !providerType) {
      console.log('Missing providerId or providerType:', { providerId, providerType })
      setError('Invalid provider information')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)
    
    try {
      console.log('=== ENHANCED FETCHPROVIDER DEBUG ===')
      console.log('Provider ID from URL:', providerId)
      console.log('Provider Type from URL:', providerType)
      console.log('Current provider state before fetch:', provider)
      
      let data: Provider[] = []
      
      console.log('Fetching provider:', { providerId, providerType })
      
      // Use real database connections for all provider types
      // Handle both singular (from URLs) and plural forms for compatibility
      switch (providerType) {
        case 'caregiver':
        case 'caregivers':
          data = await dataService.getCaregivers()
          break
        case 'companion':
        case 'companions':
          data = await dataService.getCompanions()
          break
        case 'professional':
        case 'professionals':
          data = await dataService.getProfessionals()
          break
        case 'care-checker':
        case 'care-checkers':
          data = await dataService.getCareCheckers()
          break
        default:
          throw new Error('Invalid provider type')
      }
      
      console.log('Available providers:', data.map(p => ({ id: p.id, name: p.name, idType: typeof p.id })))
      console.log('Looking for providerId:', providerId, 'Type:', typeof providerId)
      
      // Enhanced ID matching with type conversion and string comparison
      const foundProvider = data.find(p => {
        const match = p.id === providerId || String(p.id) === String(providerId)
        if (match) console.log('MATCH FOUND:', p.name, 'ID:', p.id)
        return match
      })
      
      if (!foundProvider) {
        console.error('Provider not found. Available IDs:', data.map(p => ({ id: p.id, type: typeof p.id })))
        console.error('Looking for ID:', providerId, 'Type:', typeof providerId)
        console.error('Exact comparison failed for all providers')
        throw new Error(`Provider with ID ${providerId} not found`)
      }
      
      console.log('Found provider:', foundProvider.name)
      console.log('Provider location:', foundProvider.location)
      console.log('Provider role:', foundProvider.provider_type)
      
      const processedProvider = {
        id: foundProvider.id,
        name: foundProvider.full_name || `${foundProvider.first_name || ''} ${foundProvider.last_name || ''}`.trim() || 'Unknown Provider',
        bio: foundProvider.bio || foundProvider.needs || 'No bio available',
        location: foundProvider.location || 'Location not specified',
        specialties: foundProvider.interests || [],
        verified: foundProvider.is_verified || false,
        hourly_rate: foundProvider.hourly_rate,
        years_experience: foundProvider.years_of_experience,
        rating: foundProvider.average_rating || 4.8,
        reviews_count: foundProvider.reviews_count || 0,
        provider_type: foundProvider.role,
      }
      
      console.log('Processed provider object:', processedProvider)
      console.log('About to call setProvider with:', processedProvider)
      setProvider(processedProvider)
      console.log('setProvider called - provider state should now be set')
      
      // Additional verification
      setTimeout(() => {
        console.log('Provider state after 100ms delay:', provider)
      }, 100)
      
    } catch (err) {
      console.error('fetchProvider error:', err)
      console.log('Setting error state:', err instanceof Error ? err.message : 'Failed to load provider')
      setError(err instanceof Error ? err.message : 'Failed to load provider')
    } finally {
      setLoading(false)
      console.log('Loading set to false')
      console.log('=== END ENHANCED FETCHPROVIDER DEBUG ===')
    }
  }

  const validateBookingDate = (date: string): string | null => {
    const today = new Date()
    const selectedDateObj = new Date(date)
    const todayStr = today.toISOString().split('T')[0]

    // Past date validation
    if (date < todayStr) {
      return 'Please select a future date for your appointment.'
    }

    // Maximum advance booking (90 days)
    const maxAdvanceDays = 90
    const maxDate = new Date()
    maxDate.setDate(today.getDate() + maxAdvanceDays)
    if (selectedDateObj > maxDate) {
      return `Bookings can only be made up to ${maxAdvanceDays} days in advance.`
    }

    // Weekend validation (assuming weekends are not available)
    const dayOfWeek = selectedDateObj.getDay()
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return 'Appointments are only available Monday through Friday.'
    }

    return null // No validation errors
  }

  const handleBookAppointment = () => {
    if (bookingStep === 1 && selectedDate) {
      // Comprehensive date validation
      const validationError = validateBookingDate(selectedDate)
      if (validationError) {
        alert(validationError)
        return
      }
      setBookingStep(2)
    } else if (bookingStep === 2 && selectedTime) {
      // Move to payment step
      setBookingStep(3)
    } else if (bookingStep === 3 && paymentMethod) {
      // Save booking to database with payment info
      saveBookingToDatabase()
    }
  }

  const saveBookingToDatabase = async () => {
    try {
      // Calculate start and end times
      const [time, period] = selectedTime.split(' ')
      const [hours, minutes] = time.split(':')
      let hour24 = parseInt(hours)
      if (period === 'PM' && hour24 !== 12) hour24 += 12
      if (period === 'AM' && hour24 === 12) hour24 = 0

      const startDateTime = new Date(selectedDate)
      startDateTime.setHours(hour24, parseInt(minutes || '0'), 0, 0)

      const endDateTime = new Date(startDateTime)
      endDateTime.setHours(startDateTime.getHours() + 1) // 1 hour session

      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        alert('Please log in to book an appointment.')
        return
      }

      // Check for double booking
      const { data: existingBookings, error: checkError } = await supabase
        .from('bookings')
        .select('id')
        .eq('provider_id', provider?.id)
        .eq('start_time', startDateTime.toISOString())
        .eq('status', 'pending')

      if (checkError) {
        console.error('Error checking existing bookings:', checkError)
        alert('Error checking availability. Please try again.')
        return
      }

      if (existingBookings && existingBookings.length > 0) {
        alert('This time slot is already booked. Please select a different time.')
        return
      }

      // Save to database
      const { data, error } = await supabase
        .from('bookings')
        .insert({
          user_id: user.id,
          provider_id: provider?.id,
          provider_type: providerType || 'caregiver',
          start_time: startDateTime.toISOString(),
          end_time: endDateTime.toISOString(),
          status: 'pending',
          total_cost: provider?.hourly_rate,
          payment_status: 'pending',
          notes: `Booking for ${provider?.name} on ${selectedDate} at ${selectedTime}`
        })

      if (error) {
        console.error('Error saving booking:', error)
        alert('Error saving booking. Please try again.')
        return
      }

      console.log('Booking saved successfully:', data)

      // Create notifications for booking confirmation
      await createBookingNotifications(user.id, provider?.id, data?.[0]?.id)

      setBookingStep(4)
    } catch (error) {
      console.error('Error in saveBookingToDatabase:', error)
      alert('Error saving booking. Please try again.')
    }
  }

  const createBookingNotifications = async (userId: string, providerId: string | undefined, bookingId: string) => {
    try {
      const notifications = [
        // Notification for the user (booking confirmation)
        {
          user_id: userId,
          type: 'booking_confirmation',
          title: 'Booking Confirmed',
          content: `Your appointment with ${provider?.name} has been confirmed for ${selectedDate} at ${selectedTime}.`,
          related_entity_id: bookingId,
          related_table: 'bookings',
          related_entity_type: 'booking',
          metadata: {
            booking_date: selectedDate,
            booking_time: selectedTime,
            provider_name: provider?.name
          }
        }
      ]

      // Add notification for provider if providerId exists
      if (providerId) {
        notifications.push({
          user_id: providerId,
          type: 'new_booking',
          title: 'New Booking Request',
          content: `You have a new booking request for ${selectedDate} at ${selectedTime}.`,
          related_entity_id: bookingId,
          related_table: 'bookings',
          related_entity_type: 'booking',
          metadata: {
            booking_date: selectedDate,
            booking_time: selectedTime,
            client_id: userId
          }
        })
      }

      const { error: notificationError } = await supabase
        .from('notifications')
        .insert(notifications)

      if (notificationError) {
        console.error('Error creating notifications:', notificationError)
        // Don't fail the booking if notifications fail
      } else {
        console.log('Booking notifications created successfully')
      }
    } catch (error) {
      console.error('Error in createBookingNotifications:', error)
      // Don't fail the booking if notifications fail
    }
  }

  const handleSendMessage = () => {
    // TODO: Implement actual messaging functionality
    console.log(`Opening chat with ${provider?.name}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading provider profile...</p>
        </div>
      </div>
    )
  }

  if (error || !provider) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || 'Provider not found'}</p>
          <button 
            onClick={() => navigate(-1)}
            className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Back Button */}
        <button 
          onClick={() => navigate(-1)}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-6"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to {providerType?.replace('-', ' ')}
        </button>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Provider Info */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
              {/* Header */}
              <div className="flex items-start gap-4 mb-6">
                <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-2xl font-semibold text-gray-600">
                    {provider.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h1 className="text-2xl font-bold text-gray-900">{provider.name}</h1>
                    {provider.verified && (
                      <CheckCircle className="h-5 w-5 text-teal-600" />
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>{provider.rating || '4.8'}</span>
                      <span>({provider.reviews_count || '23'} reviews)</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      <span>{provider.location}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-4 text-sm">
                    <span className="font-medium text-teal-600">
                      ${provider.hourly_rate}/hour
                    </span>
                    <span className="text-gray-600">
                      {provider.years_experience} years experience
                    </span>
                  </div>
                </div>
              </div>

              {/* Bio */}
              <div className="mb-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">About</h2>
                <p className="text-gray-600 leading-relaxed">{provider.bio}</p>
              </div>

              {/* Specialties */}
              <div className="mb-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">Specialties</h2>
                <div className="flex flex-wrap gap-2">
                  {provider.specialties.map((specialty, index) => (
                    <span
                      key={index}
                      className="bg-teal-50 text-teal-700 px-3 py-1 rounded-full text-sm"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>

              {/* Additional Information */}
              <div className="mb-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">Additional Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Background Check</h3>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-gray-600">Verified & Current</span>
                    </div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Languages</h3>
                    <p className="text-sm text-gray-600">English, Spanish</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Insurance</h3>
                    <p className="text-sm text-gray-600">Liability & Bonded</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Availability</h3>
                    <p className="text-sm text-gray-600">Weekdays & Weekends</p>
                  </div>
                </div>
              </div>

              {/* Contact Actions */}
              <div className="mb-6">
                <div className="flex gap-3">
                  <button className="flex-1 bg-teal-600 text-white px-4 py-3 rounded-lg hover:bg-teal-700 transition-colors flex items-center justify-center gap-2">
                    <MessageCircle className="h-4 w-4" />
                    Send Message
                  </button>
                  <button className="flex-1 border border-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors">
                    Save Profile
                  </button>
                </div>
              </div>

              {/* Reviews Section */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-3">Recent Reviews</h2>
                <div className="space-y-4">
                  {/* Mock reviews */}
                  <div className="border-l-2 border-teal-200 pl-4">
                    <div className="flex items-center gap-1 mb-1">
                      {[1,2,3,4,5].map(star => (
                        <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-gray-600 text-sm mb-1">
                      "Excellent care and very professional. Highly recommend!"
                    </p>
                    <p className="text-xs text-gray-500">- Sarah M., 2 weeks ago</p>
                  </div>
                  <div className="border-l-2 border-teal-200 pl-4">
                    <div className="flex items-center gap-1 mb-1">
                      {[1,2,3,4,5].map(star => (
                        <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-gray-600 text-sm mb-1">
                      "Very caring and attentive. Made my father feel comfortable."
                    </p>
                    <p className="text-xs text-gray-500">- Michael R., 1 month ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Panel */}
          <div className="xl:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm sticky top-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Book Appointment</h2>
              
              {bookingStep === 1 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Date
                  </label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 mb-4"
                  />
                  <button
                    onClick={handleBookAppointment}
                    disabled={!selectedDate}
                    className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-teal-700 disabled:bg-gray-300 disabled:cursor-not-allowed mb-3"
                  >
                    Continue
                  </button>
                </div>
              )}

              {bookingStep === 2 && (
                <div>
                  <button
                    onClick={() => setBookingStep(1)}
                    className="text-sm text-gray-600 hover:text-gray-800 mb-3 flex items-center gap-1"
                  >
                    <ArrowLeft className="h-3 w-3" />
                    Change date
                  </button>
                  <p className="text-sm text-gray-600 mb-3">Date: {selectedDate}</p>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Time
                  </label>
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {availableTimeSlots.map((slot) => (
                      <button
                        key={slot}
                        onClick={() => setSelectedTime(slot)}
                        className={`p-2 text-sm rounded border ${
                          selectedTime === slot
                            ? 'bg-teal-600 text-white border-teal-600'
                            : 'bg-white text-gray-700 border-gray-300 hover:border-teal-600'
                        }`}
                      >
                        {slot}
                      </button>
                    ))}
                  </div>
                  <button
                    onClick={handleBookAppointment}
                    disabled={!selectedTime}
                    className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-teal-700 disabled:bg-gray-300 disabled:cursor-not-allowed mb-3"
                  >
                    Book Appointment
                  </button>
                </div>
              )}

              {bookingStep === 3 && (
                <div>
                  <button
                    onClick={() => setBookingStep(2)}
                    className="text-sm text-gray-600 hover:text-gray-800 mb-3 flex items-center gap-1"
                  >
                    <ArrowLeft className="h-3 w-3" />
                    Change time
                  </button>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Payment Information</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Appointment: {selectedDate} at {selectedTime}
                  </p>
                  <p className="text-sm font-medium text-gray-900 mb-4">
                    Total: ${provider?.hourly_rate ? `${provider.hourly_rate}.00` : 'Contact for pricing'}
                  </p>

                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Method
                  </label>
                  <select
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 mb-4"
                  >
                    <option value="">Select payment method</option>
                    <option value="credit_card">Credit Card</option>
                    <option value="debit_card">Debit Card</option>
                    <option value="paypal">PayPal</option>
                    <option value="insurance">Insurance</option>
                  </select>

                  <button
                    onClick={handleBookAppointment}
                    disabled={!paymentMethod}
                    className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-teal-700 disabled:bg-gray-300 disabled:cursor-not-allowed mb-3"
                  >
                    Confirm & Pay
                  </button>
                </div>
              )}

              {bookingStep === 4 && (
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 text-teal-600 mx-auto mb-3" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Booking Confirmed!</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Your appointment with {provider.name} is scheduled for {selectedDate} at {selectedTime}.
                  </p>
                  <p className="text-sm text-gray-600 mb-4">
                    Payment method: {paymentMethod?.replace('_', ' ')}
                  </p>
                  <button
                    onClick={() => navigate(-1)}
                    className="w-full bg-teal-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-teal-700"
                  >
                    Back to Search
                  </button>
                </div>
              )}

              {bookingStep < 4 && (
                <>
                  <button
                    onClick={handleSendMessage}
                    className="w-full bg-white text-teal-600 border border-teal-600 py-2 px-4 rounded-lg font-medium hover:bg-teal-50 mb-3 flex items-center justify-center gap-2"
                  >
                    <MessageCircle className="h-4 w-4" />
                    Send Message
                  </button>
                  
                  <div className="text-xs text-gray-500 text-center">
                    <Clock className="h-3 w-3 inline mr-1" />
                    Usually responds within 2 hours
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
