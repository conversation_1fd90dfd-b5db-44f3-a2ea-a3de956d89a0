import { supabase } from './supabase'

// Clean, minimal data service using REAL tables from care_connector schema
export const dataService = {
  // Caregivers - using profiles table with role filtering (schema set in supabase config)
  async getCaregivers() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .eq('role', 'caregiver')

      if (error) {
        console.error('Database error fetching caregivers:', error)
        throw error
      }

      // Map database fields to expected interface
      const mappedData = (data || []).map(profile => ({
        id: profile.id,
        name: profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
        bio: profile.bio || 'Experienced caregiver dedicated to providing compassionate and professional care.',
        location: profile.location || 'Location not specified',
        specialties: profile.specialties || ['General Care', 'Companionship'],
        verified: profile.is_verified || false,
        provider_type: 'caregiver',
        hourly_rate: profile.hourly_rate || 35,
        years_experience: profile.years_of_experience || 0,
        profile_image: profile.avatar_url,
        rating: parseFloat(profile.average_rating) || 4.5,
        reviews_count: profile.reviews_count || 0,
        availability: ['Weekdays', 'Weekends']
      }))

      return mappedData
    } catch (error) {
      console.error('Error in getCaregivers:', error)
      throw error
    }
  },

  async getCaregiver(id: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching caregiver:', error)
      throw error
    }

    // Map database fields to expected interface
    const mappedData = {
      id: data.id,
      name: data.full_name || `${data.first_name || ''} ${data.last_name || ''}`.trim(),
      bio: data.bio || 'Experienced caregiver dedicated to providing compassionate and professional care.',
      location: data.location || 'Location not specified',
      specialties: data.specialties || ['General Care', 'Companionship'],
      verified: data.is_verified || false,
      provider_type: 'caregiver',
      hourly_rate: data.hourly_rate || 35,
      years_experience: data.years_of_experience || 0,
      profile_image: data.avatar_url,
      rating: parseFloat(data.average_rating) || 4.5,
      reviews_count: data.reviews_count || 0,
      availability: ['Weekdays', 'Weekends']
    }

    return mappedData
  },

  // Companions - using profiles table (schema set in supabase config)
  async getCompanions() {
    const { data, error } = await supabase
      .schema('care_connector')
      .from('profiles')
      .select('*')
      .eq('role', 'companion')

    if (error) {
      console.error('Error fetching companions:', error)
      throw error
    }
    return data || []
  },

  // Professionals - using profiles table (schema set in supabase config)
  async getProfessionals() {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('role', 'professional')

    if (error) {
      console.error('Error fetching professionals:', error)
      throw error
    }
    return data || []
  },

  // Care Checkers - using profiles table (schema set in supabase config)
  async getCareCheckers() {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('role', 'care_checker')

    if (error) {
      console.error('Error fetching care checkers:', error)
      throw error
    }
    return data || []
  },

  // Care Groups - using actual care_groups table with privacy filter matching original app
  async getCareGroups() {
    const { data, error } = await supabase
      .from('care_connector.care_groups')
      .select(`
        id,
        name,
        description,
        privacy_setting,
        member_count,
        created_at,
        category_id,
        avatar_url
      `)
      .eq('privacy_setting', 'public')
      .limit(20)
      .order('created_at', { ascending: false })

    if (error) throw error

    // Process the data to add missing required fields like original app
    const processedData = (data || []).map(group => ({
      ...group,
      member_count: group.member_count || 0,
      is_member: false,
      category: group.category_id || 'General'
    }))

    return processedData
  },

  // Homepage Statistics - fetch real data from database tables
  async getHomepageStats() {
    try {
      // Get total verified professionals count
      const { count: professionalsCount } = await supabase
        .from('profiles')
        .select('id', { count: 'exact', head: true })
        .in('role', ['caregiver', 'companion', 'professional', 'care_checker'])
        .eq('is_verified', true)

      // Get average rating from reviews/ratings table
      const { data: ratingsData } = await supabase
        .from('reviews')
        .select('rating')
        .not('rating', 'is', null)

      const averageRating = ratingsData && ratingsData.length > 0
        ? (ratingsData.reduce((sum, review) => sum + review.rating, 0) / ratingsData.length).toFixed(1)
        : null

      // Get total successful bookings count
      const { count: bookingsCount } = await supabase
        .from('bookings')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'completed')

      return {
        verifiedProfessionals: professionalsCount || 0,
        averageRating: averageRating,
        successfulBookings: bookingsCount || 0
      }
    } catch (error) {
      console.error('Error fetching homepage stats:', error)
      // Return error state values if database query fails - no fake data allowed
      return {
        verifiedProfessionals: 0,
        averageRating: null,
        successfulBookings: 0
      }
    }
  }
}
