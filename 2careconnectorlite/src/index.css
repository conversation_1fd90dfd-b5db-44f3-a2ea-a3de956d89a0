@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== COLORS ONLY - NO SIZING/SPACING/LAYOUT ===== */
:root {
  /* Brand Colors - ONE SHADE ONLY per Holy Rule 3 */
  --primary: #059669;
  
  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  
  /* Background Colors - Apple Mac Desktop Style */
  --bg-primary: #fafafa;
  --bg-secondary: #f5f5f7;
  --bg-accent: #f0fdf4;
  
  /* Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;

  /* Focus Colors */
  --focus-shadow: rgba(5, 150, 105, 0.1);
}

/* ===== FONT CHARACTER ONLY - NO SIZING ===== */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* ===== ICON STYLES ONLY ===== */
.icon {
  color: var(--primary);
}

.icon-secondary {
  color: var(--text-secondary);
}

/* ===== BUTTON COLOR STYLES ONLY ===== */
.button-primary {
  background-color: var(--primary);
  color: var(--bg-primary);
}

.button-primary:hover {
  background-color: var(--primary);
  opacity: 0.9;
}

/* ===== DROPDOWN LINK COLOR STYLES ONLY ===== */
.dropdown-link {
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

.dropdown-link:hover {
  background-color: var(--bg-secondary);
}

/* ===== DASHBOARD CARD COLOR STYLES ONLY ===== */
.dashboard-card {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

.dashboard-card:hover {
  background-color: var(--bg-primary);
}

/* ===== SIDEBAR BUTTON COLOR STYLES ONLY ===== */
.sidebar-button {
  color: var(--text-secondary);
  background-color: transparent;
}

.sidebar-button:hover {
  background-color: var(--bg-secondary);
  color: var(--primary);
}

.sidebar-button.active {
  background-color: var(--bg-accent);
  color: var(--primary);
}

/* ===== MACOS SIDEBAR NAVIGATION COLOR STYLES ONLY ===== */
.macos-sidebar-item {
  color: var(--text-primary);
  background-color: transparent;
}

.macos-sidebar-item:hover {
  background-color: var(--bg-secondary);
}

.macos-sidebar-item.active {
  background-color: var(--primary);
  color: var(--bg-primary);
}

.macos-sidebar-item.active:hover {
  background-color: var(--primary);
  color: var(--bg-primary);
}

/* ===== APPLE MAC DESKTOP LAYOUT COLOR STYLES ONLY ===== */
.macos-dashboard-layout {
  background-color: var(--bg-secondary);
}

.macos-sidebar {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

.macos-main-content {
  background-color: var(--bg-primary);
}

.macos-content-area {
  background-color: var(--bg-secondary);
}

/* ===== PREMIUM ENTRANCE EFFECTS AND MICRO-ANIMATIONS ===== */

/* Keyframe Definitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUpOnScroll {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Base Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-up-delay-1 {
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
  opacity: 0;
}

.animate-fade-in-up-delay-2 {
  animation: fadeInUp 0.8s ease-out 0.4s forwards;
  opacity: 0;
}

.animate-fade-in-up-delay-3 {
  animation: fadeInUp 0.8s ease-out 0.6s forwards;
  opacity: 0;
}

.animate-fade-in-up-on-scroll {
  animation: fadeInUpOnScroll 0.6s ease-out forwards;
  opacity: 0;
}

.animate-stagger-children > * {
  animation: fadeInUpOnScroll 0.5s ease-out forwards;
  opacity: 0;
}

.animate-stagger-children > *:nth-child(1) {
  animation-delay: 0.1s;
}

.animate-stagger-children > *:nth-child(2) {
  animation-delay: 0.2s;
}

.animate-stagger-children > *:nth-child(3) {
  animation-delay: 0.3s;
}

.animate-stagger-children > *:nth-child(4) {
  animation-delay: 0.4s;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-fade-in-up-delay-1,
  .animate-fade-in-up-delay-2,
  .animate-fade-in-up-delay-3,
  .animate-fade-in-up-on-scroll,
  .animate-stagger-children > * {
    animation: none;
    opacity: 1;
    transform: none;
  }
}
